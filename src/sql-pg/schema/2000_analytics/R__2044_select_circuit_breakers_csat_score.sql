-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.circuit_breakers_csat_score;
drop function if exists analytics.select_circuit_breakers_csat_score;
create or replace function analytics.select_circuit_breakers_csat_score(
    p_date date default current_date - interval '2 day',
    p_n_order_items numeric default 100,
    p_csat_points numeric default 2,
    p_country text default 'all'
)
/*
    :p_date: the date for which to retrieve data, defaults to 2 days ago.
    :p_n_order_items: minimum number of order items a merchant must have to be included, defaults to 100.
    :p_csat_points: maximum CSAT points allowed for merchants to be included, defaults to 2.
    :p_country: which country of the MPS per country to query, defaults to all.
*/
returns table (merchant_id int, merchant_name text, primary_email text)
as
$$
select
    mh.id as merchant_id,
    mh.name as merchant_name,
    p.primary_email
from
    analytics.active_merchants as m
inner join
    analytics.mps_per_country_combined_history as mh
on m.id = mh.id
left join
    analytics.pipedrive as p
on m.id = p.merchant_id
where
    mh.date = date(p_date)
    and mh.n_order_items >= p_n_order_items
    and mh.csat_points < p_csat_points
    and mh.country = p_country
    and p.has_performance_role = True
group by mh.id, mh.name, p.primary_email;
$$ language sql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_circuit_breakers_csat_score() limit 10;
    end if;
end;
$test$;
