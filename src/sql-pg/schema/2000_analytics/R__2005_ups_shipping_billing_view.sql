-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists analytics.safe_cast_to_int cascade;
create function analytics.safe_cast_to_int(text_val text) returns int
language plpgsql
as
$$
begin
    return text_val::integer;
exception when others then
    return null;
end;
$$;

drop view if exists analytics.ups_shipping_billing_items_tracking_links;
create or replace view analytics.ups_shipping_billing_items_tracking_links
/**
    The view ups_shipping_billing_items_tracking_links is used by analytics
    to build the corresponding Looker view that finance can use to bill suppliers
    under a shipping agreement accordingly.

    In terms of functionality, we attempt to match all billings from UPS with orderItemIDs
    based on tracking numbers stored in shipment_tracking.
    If no tracking number corresponds to a billing record, we check if shipment_reference_1 contains an orderItemID.

    Additionally, there's a condition involving an arbitrary 40 days.
    We implement this because carriers sometimes reuse tracking numbers,
    and in order to avoid invalid matching, we allow matching of billing only with tracking links
    that were created a maximum of 40 days before. Carriers might reuse their tracking numbers after 90 days.
*/
as
with billing_expanded as not materialized (
    select
        sb.id as billing_id,
        sb.tracking_number,
        sb.transaction_date,
        analytics.safe_cast_to_int(unnest_order_item_id) as order_item_id
    from analytics.shipping_billing as sb,
        lateral unnest(
            string_to_array(sb.shipment_reference_1, ',')
        ) as unnest_order_item_id
    where analytics.safe_cast_to_int(unnest_order_item_id) is not null
),

matched_by_tracking_number as not materialized (
    select
        sb.id as billing_id,
        oid2.order_item_id
    from analytics.shipping_billing as sb
    left join public.shipment_tracking as st
    on sb.tracking_number = st.tracking_number
    left join public.order_item_details as oid2
    on
        st.id = oid2.shipment_tracking_id
        and oid2.valid_to = 'infinity'
    where sb.transaction_date <= st.created_at + interval '40 days'
)

select distinct
    sb.id as billing_id,
    sb.tracking_number,
    coalesce(m.order_item_id, be.order_item_id) as order_item_id
from analytics.shipping_billing as sb
left join matched_by_tracking_number as m
on sb.id = m.billing_id
left join billing_expanded as be
on sb.id = be.billing_id
where
    sb.tracking_number is not null
    and (
        m.order_item_id is not null
        or be.order_item_id is not null
    )
order by sb.id desc;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform
            billing_id,
            tracking_number,
            order_item_id
        from
            analytics.ups_shipping_billing_items_tracking_links
        limit 10;
    end if;
end;
$test$;
