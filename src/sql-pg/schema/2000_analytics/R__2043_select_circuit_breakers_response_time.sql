-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.circuit_breakers_response_time;
drop function if exists analytics.select_circuit_breakers_response_time;
create or replace function analytics.select_circuit_breakers_response_time(
    p_response_time_10h_points numeric default 4,
    p_n_order_items numeric default 100,
    p_country text default 'all',
    p_date date default current_date - 2,
    messages_num int default 3
)
/*
    :p_response_time_10h_points: maximum threshold for response_time_10h points, defaults to 4.
    :p_n_order_items: minimum number of order items a merchant must have to be included, defaults to 100.
    :p_country: which country of the MPS per country to query, defaults to all.
    :p_date: the date for which to retrieve data, defaults to 2 days ago.
    :messages_num: minimum number of messages that must exceed the response time threshold, defaults to 3.
*/
returns table (merchant_id int, merchant_name text, primary_email text)
as
$$
select
    mh.id as merchant_id,
    mh.name as merchant_name,
    p.primary_email
from
    analytics.active_merchants as m
inner join
    analytics.mps_per_country_combined_history as mh
on m.id = mh.id
left join
    analytics.pipedrive as p
on m.id = p.merchant_id
where
    mh.response_time_10h_points <= p_response_time_10h_points
    and mh.n_order_items >= p_n_order_items
    and mh.country = p_country
    and mh.date = date(p_date)
    and round(mh.s_messages * (1 - mh.ten_h)) > messages_num
    and p.has_performance_role = True
group by
    mh.id, mh.name, p.primary_email, round(mh.s_messages * (1 - mh.ten_h));
$$ language sql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_circuit_breakers_response_time() limit 10;
    end if;
end;
$test$;
