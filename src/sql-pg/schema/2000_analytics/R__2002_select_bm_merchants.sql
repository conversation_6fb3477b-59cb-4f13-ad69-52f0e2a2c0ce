-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists analytics.select_merchants_bm_listing();
/**
  return list of merchants to be used in bm_listing matching
*/
create or replace function analytics.select_merchants_bm_listing()
returns table (merchant_id int, ref_merchant_name text) as
$function$
begin
    return query
    select
        id as merchant_id,
        lower(replace(name, ' ', '')) as ref_merchant_name
    from
        public.merchants;
end;
$function$ language plpgsql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_merchants_bm_listing() limit 10;
    end if;
end;
$test$;
