-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics.upsert_dhl_shipping_billing;
/**
  Insert or update analytics.dhl_shipping_billing from the staging import.dhl_shipping_billing table.
*/
create or replace procedure analytics.upsert_dhl_shipping_billing()
language SQL
as $$
    insert into analytics.dhl_shipping_billing(
        shipment_number,
        source_files_id,
        package_type,
        line_type,
        billing_source,
        billing_account,
        billing_country_code,
        invoice_date,
        invoice_number,
        shipment_date,
        shipment_reference_1,
        shipment_reference_2,
        shipment_reference_3,
        weight_kg,
        weight_flag,
        senders_name,
        senders_country,
        receivers_country,
        currency,
        amount_net,
        amount_gross
    )
    select
        distinct on (isb.shipment_number) shipment_number,
        sf.id,
        isb.package_type,
        isb.line_type,
        isb.billing_source,
        isb.billing_account,
        isb.billing_country_code,
        isb.invoice_date,
        isb.invoice_number,
        isb.shipment_date,
        isb.shipment_reference_1,
        isb.shipment_reference_2,
        isb.shipment_reference_3,
        isb.weight_kg,
        isb.weight_flag,
        isb.senders_name,
        isb.senders_country,
        isb.receivers_country,
        isb.currency,
        isb.amount_net,
        isb.amount_gross
    from
        import.dhl_shipping_billing as isb
    inner join
        analytics.source_files as sf on isb.file_path = sf.file_path
    where
        trim(isb.shipment_number) != ''
    order by
        -- in case of duplicate shipment_number favour most recent shipment
        isb.shipment_number, isb.shipment_date desc
    on conflict(shipment_number) do update
    set (
        source_files_id,
        package_type,
        line_type,
        billing_source,
        billing_account,
        billing_country_code,
        invoice_date,
        invoice_number,
        shipment_date,
        shipment_reference_1,
        shipment_reference_2,
        shipment_reference_3,
        weight_kg,
        weight_flag,
        senders_name,
        senders_country,
        receivers_country,
        currency,
        amount_net,
        amount_gross
    ) = (
        excluded.source_files_id,
        excluded.package_type,
        excluded.line_type,
        excluded.billing_source,
        excluded.billing_account,
        excluded.billing_country_code,
        excluded.invoice_date,
        excluded.invoice_number,
        excluded.shipment_date,
        excluded.shipment_reference_1,
        excluded.shipment_reference_2,
        excluded.shipment_reference_3,
        excluded.weight_kg,
        excluded.weight_flag,
        excluded.senders_name,
        excluded.senders_country,
        excluded.receivers_country,
        excluded.currency,
        excluded.amount_net,
        excluded.amount_gross
    );
$$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        call analytics.upsert_dhl_shipping_billing();
    end if;
end;
$test$;
