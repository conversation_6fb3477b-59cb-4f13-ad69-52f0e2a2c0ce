-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists analytics.shipping_billing_order_items;
create or replace view analytics.shipping_billing_order_items
/**
    Aggregates data from the following tables:
    - analytics.dhl_shipping_billing,
    - analytics.dhl_order_items
*/
as
select
    doi.shipping_billing_id,
    doi.order_item_id,
    dsb.shipment_number,
    dsb.shipment_date
from
    analytics.dhl_shipping_billing as dsb
inner join
    analytics.dhl_order_items as doi
on dsb.id = doi.shipping_billing_id;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform
            shipping_billing_id,
            order_item_id
        from analytics.shipping_billing_order_items limit 10;
    end if;
end;
$test$;
