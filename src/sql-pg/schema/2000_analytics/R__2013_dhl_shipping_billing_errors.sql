-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists analytics.shipping_billing_errors;
create or replace view analytics.shipping_billing_errors
/**
    Report on the following data anomalies:
    - missing order item ids
    - missing shipment date
    - missing invoice date or number
    - missing sender (supplier) name
    - missing currency or amount
*/
as
with shipping_files as not materialized (
    select
        dsb.id,
        dsb.billing_source,
        dsb.billing_account,
        dsb.shipment_number,
        dsb.shipment_date,
        dsb.invoice_date,
        dsb.invoice_number,
        dsb.senders_name,
        dsb.currency,
        dsb.amount_net,
        dsb.amount_gross,
        sf.file_path,
        sf.file_created
    from
        analytics.dhl_shipping_billing as dsb
    left join
        analytics.source_files as sf
    on dsb.source_files_id = sf.id
),

errors as materialized (
    select
        'Missing order item(s)' as error,
        sf.*
    from
        shipping_files as sf
    where not exists (
            -- `select from` is the correct syntax in postgres!
            select from analytics.dhl_order_items as doi
            where doi.shipping_billing_id = sf.id
        )
    union all
    select
        'Missing shipment date' as error,
        sf.*
    from
        shipping_files as sf
    where
        sf.shipment_date is null
    union all
    select
        'Missing invoice date or number' as error,
        sf.*
    from
        shipping_files as sf
    where
        sf.invoice_date is null
        or coalesce(sf.invoice_number, '') = ''
    union all
    select
        'Missing sender (supplier) name' as error,
        sf.*
    from
        shipping_files as sf
    where
        coalesce(sf.senders_name, '') = ''
    union all
    select
        'No currency or amount' as error,
        sf.*
    from
        shipping_files as sf
    where
        coalesce(sf.currency, '') = ''
        or coalesce(sf.amount_net, 0) = 0
        or coalesce(sf.amount_gross, 0) = 0
)

select
    error,
    id,
    billing_source,
    billing_account,
    shipment_number,
    file_path,
    file_created
from
    errors;

-- test: select * from analytics.shipping_billing_errors
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform *
        from
            analytics.shipping_billing_errors
        limit 5;
    end if;
end;
$test$;
