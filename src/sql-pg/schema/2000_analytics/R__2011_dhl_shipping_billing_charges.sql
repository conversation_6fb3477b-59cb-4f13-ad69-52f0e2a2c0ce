-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists analytics.shipping_billing_charges;
create or replace view analytics.shipping_billing_charges
/**
    Aggregates data from the following tables:
    - analytics.dhl_shipping_billing,
    - analytics.dhl_shipping_charges,
    - analytics.source_files
*/
as
select
    -- Id and provider
    dsb.id,
    'dhl' as shipping_provider,

    -- Shipment fields
    dsb.shipment_date,
    dsb.shipment_number,
    dsb.shipment_reference_1,
    dsb.shipment_reference_2,
    dsb.shipment_reference_3,

    -- Billing source
    dsb.billing_source,
    dsb.billing_account,
    dsb.billing_country_code,

    -- Currency and amounts
    dsb.currency,
    dsb.amount_net,
    dsb.amount_gross,
    dsc.total_net as charges_net,
    dsc.total_gross as charges_gross,
    dsb.package_type,
    dsb.line_type,

    dsb.invoice_date,
    dsb.invoice_number,

    dsb.senders_name,
    dsb.senders_country,

    dsb.receivers_country,
    dsb.weight_kg,
    dsb.weight_flag,

    dsc.carbon_reduced_fe_net,
    dsc.carbon_reduced_fe_gross,

    -- CHARGES alphabetically
    dsc.carbon_reduced_fd_net,
    dsc.carbon_reduced_fd_gross,
    dsc.carbon_reduced_ft_net,
    dsc.carbon_reduced_ft_gross,
    dsc.change_of_billing_net,
    dsc.change_of_billing_gross,

    dsc.demand_surcharge_net,
    dsc.demand_surcharge_gross,
    dsc.duty_tax_paid_net,
    dsc.duty_tax_paid_gross,

    dsc.emergency_situation_net,
    dsc.emergency_situation_gross,

    dsc.export_declaration_net,
    dsc.export_declaration_gross,

    dsc.fuel_surcharge_net,
    dsc.fuel_surcharge_gross,

    dsc.import_export_duties_net,
    dsc.import_export_duties_gross,

    dsc.import_export_taxes_net,
    dsc.import_export_taxes_gross,

    dsc.oversize_piece_net,
    dsc.oversize_piece_gross,

    dsc.overweight_piece_net,
    dsc.overweight_piece_gross,

    dsc.plastic_flyer_net,
    dsc.plastic_flyer_gross,

    dsc.shipment_insurance_net,
    dsc.shipment_insurance_gross,

    dsc.remote_area_delivery_net,
    dsc.remote_area_delivery_gross,

    dsc.remote_area_pickup_net,
    dsc.remote_area_pickup_gross,

    -- SOURCE FILE META-DATA
    dsb.source_files_id,
    sf.file_path,
    sf.file_created,
    sf.file_size
from
    analytics.dhl_shipping_billing as dsb
left join
    analytics.dhl_shipping_charges as dsc
on dsb.id = dsc.shipping_billing_id
left join
    analytics.source_files as sf
on dsb.source_files_id = sf.id;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform
            id,
            shipping_provider,
            shipment_number
        from analytics.shipping_billing_charges limit 10;
    end if;
end;
$test$;
