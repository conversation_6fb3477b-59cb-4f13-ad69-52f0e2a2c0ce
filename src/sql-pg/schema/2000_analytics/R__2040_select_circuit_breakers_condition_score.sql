-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.circuit_breakers_condition_score;
drop function if exists analytics.select_circuit_breakers_condition_score;
create or replace function analytics.select_circuit_breakers_condition_score(
    p_n_order_items numeric default 100,
    p_first_order_date date default current_date - interval '60 days',
    p_count_condition numeric default 5,
    p_condition_points_per_grading numeric default 6,
    p_date date default date_trunc('week', current_date)::date - interval '2 day',
    p_country text default 'all'
)
/*
    :p_n_order_items: minimum number of order items a merchant must have to be included, defaults to 100.
    :p_first_order_date: merchants with first order before this date will be included, defaults to 60 days ago.
    :p_count_condition: minimum count of condition issues a merchant must have to be included, defaults to 5.
    :p_condition_points_per_grading: maximum condition points per grading allowed, defaults to 6.
    :p_date: the date for which to retrieve data, defaults to 2 days before the start of the current week.
    :p_country: which country of the MPS per country to query, defaults to all.
*/
returns table (merchant_id int, merchant_name text, primary_email text)
as
$$
select
    mh.id as merchant_id,
    mh.name as merchant_name,
    p.primary_email
from
    analytics.active_merchants as m
inner join
    analytics.mps_per_country_combined_history as mh
on m.id = mh.id
left join analytics.pipedrive p
on m.id = p.merchant_id
where
    mh.n_order_items >= p_n_order_items
    and mh.first_order_date < p_first_order_date
    and mh.count_condition > p_count_condition
    and mh.condition_points_per_grading <= p_condition_points_per_grading
    and mh.date = p_date
    and mh.country = p_country
    and p.has_performance_role = True
group by mh.id, mh.name, p.primary_email;
$$ language sql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_circuit_breakers_condition_score() limit 10;
    end if;
end;
$test$;
