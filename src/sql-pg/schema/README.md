## Database schemas

### Domain specific schemas

Public schemas, use case specific.

- `platform_export`:
  - views only,
  - created in the live `platform` database,
  - expose used tables/columns only with cross-platform data types,
  - can be exported to BigQuery as staging tables into dedicated `platform_export` dataset,
- `export_etl`:
  - functions and views,
  - created in the clone of production database and analytics database,
  - might contain additional logic or calculations to prepare data for ETL jobs,
  - can be exported to BigQuery as staging tables into dedicated `platform_export` dataset,
  - allow DE/DA/DS teams to introduce some logic not present in raw platform tables,
- `analytics`:
    - functions, views and tables,
    - created in the clone of production database and analytics database,
    - contains final logic and physical tables for DA Looker on top of Postgres.-

### Technical schemas

Technical, internal schemas, for power users only.

- `monitoring`:
  - functions for monitoring purposes only, i.e. `who_is_active`,
- `log`:
  - tables and functions to store and log ETL (pipeline) execution,
- `maintenance`:
  - functions for managing user access or timeout queries,

## Export views

Export views provide interface to platform tables:
- Created in a dedicated `platform_export` schema;
- Limited to used, public columns only;
- Expose cross-platform data types that can be consumed by BigQuery SQL;
- Raise schema errors for breaking changes;
- Will be deployed to platform database via goose migrations

References:
- [Engineering Group Meeting](https://www.notion.so/refurbed/Engineering-Group-bd7749bcabf0462f8a6ee55d1572177a?source=copy_link#22cb6a8983ab8022a1b7def2d7763f93)
- [Google Slides](https://docs.google.com/presentation/d/1pHUjlMFJ_oA-QUHsi_VPN-t-I1GVjy1HLozmsA98CuQ/edit?usp=sharing)

### Usage

Export views are intended to be queried only by the ETL jobs via clone of production database server,
and not live production database!

There are no plans for Looker or any other BI tool to use these views directly from live production database.

References:
- [Sub-hour-data-freshness](https://www.notion.so/refurbed/Sub-hour-data-freshness-1ffb6a8983ab802983a2ec8d3f673062?source=copy_link#226b6a8983ab801394ceeca3ac8d67d1)

### Export types

There are 3 export types:
- `incremental`: uses `id`, `updated_at`, `deleted_at` fields to identify data increment to load;
- `time travel`: uses `id`, `valid_from`, `valid_to` fields to identify data increment to load;
- `snapshot`: full data export;

References:
- https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
