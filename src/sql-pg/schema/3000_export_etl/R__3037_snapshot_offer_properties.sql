-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.offer_properties;
create or replace view export_etl.offer_properties
/*
    Provides offer properties, `battery condition` so far:
    - Use public.offer_properties for new battery_condition
    - Use public.offers, public.instances for historical `neuer Akku`

    Performance note:
    - key on offer_id, as otherwise you'll get duplicates
    - offer_valid_from is only used to join with offers
    - not materialized performs better
*/
as
with new_offer_properties as not materialized (
    select distinct on (offer_id) -- PK
        offer_id,
        offer_valid_from,
        created_at,
        battery_condition,
        is_new_battery
    from
        platform_export.offer_properties
    order by
        offer_id asc,
        offer_valid_from desc,  -- Get the latest offer_valid_from
        created_at desc         -- If multiple records for same offer_valid_from, get latest created
),

old_offer_properties as not materialized (
    select distinct on (o.id) -- PK
        o.id as offer_id,
        o.valid_from as offer_valid_from,
        i.created_at,
        'new' as battery_condition,
        true as is_new_battery
    from
        platform_export.offers as o
    inner join
        platform_export.instances as i
    on o.instance_id = i.id
    left join
        new_offer_properties as nop
    on o.id = nop.offer_id
    where
        i.name like '%neuer Akku%'
        and nop.offer_id is null
    order by
        o.id asc,
        o.valid_from desc,
        i.created_at desc
)

select
    offer_id,
    offer_valid_from,
    created_at,
    battery_condition,
    is_new_battery
from new_offer_properties
union all
select
    offer_id,
    offer_valid_from,
    created_at,
    battery_condition,
    is_new_battery
from old_offer_properties;

/* test: select * from export_etl.offer_properties limit 5;

* PROD with full offers:
- not materialized: 1,088,320 rows affected in 1 m 27 s 595 ms
- materialized: 1,088,320 rows affected in 1 m 43 s 86 ms

* Analytics with 6m offers: 1,073,226 rows affected in 39 s 552 ms

create schema if not exists test;
drop table if exists test.offer_properties;
create table test.offer_properties
as
select * from export_etl.offer_properties;
*/
