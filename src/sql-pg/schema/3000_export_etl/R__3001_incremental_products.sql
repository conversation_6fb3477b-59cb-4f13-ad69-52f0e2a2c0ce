-- Flyway will re-run this script if it is changed or ${changeReason} is updated
do
$$
begin
    if ('${flyway:environment}' in ('local', 'cicd-docker-build')) then
    -- Use physical table instead of view for testing purposes (mocking view content)

        if exists (select * from pg_views where viewname = 'products' and schemaname = 'export_etl') then
            drop view export_etl.products;
        end if;

        if exists (select * from pg_tables where tablename = 'products' and schemaname = 'export_etl') then
            drop table export_etl.products;
        end if;

        create unlogged table export_etl.products
        (
            id integer primary key,
            created_at timestamp with time zone not null,
            updated_at timestamp with time zone not null,
            deleted_at timestamp with time zone,

            category_id integer not null,
            listing_mode text not null,
            name text not null,
            name_en text,
            slug text
        );

        insert into export_etl.products
        select
            id,
            created_at,
            updated_at,
            deleted_at,
            category_id,
            listing_mode,
            name,
            name_en,
            slug
        from platform_export.products;
    else
        drop view if exists export_etl.products;
        create or replace view export_etl.products
        as
        select
            id,
            created_at,
            updated_at,
            deleted_at,
            category_id,
            listing_mode,
            name,
            name_en,
            slug
        from platform_export.products;
    end if;
end
$$;

-- test: select * from export_etl.products limit 5;
