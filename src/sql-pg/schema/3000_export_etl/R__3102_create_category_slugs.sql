-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists export_etl.create_category_slugs cascade;
create procedure export_etl.create_category_slugs(debug bool = false)
/**
  Create categories with slug per country using dynamic SQL as slugs can be added with new countries.
  As this table is re-created each time do not use it as part of any SQL object (like view).
  This explains use of internal `platform_export` schema instead of `analytics`.
*/
language plpgsql as
$$
declare
    sql_text text;
begin
    select
        'create table export_etl.category_slugs as ' ||
        'select id, ' || string_agg(distinct column_name, ', ') || ' from public.categories;'
    into sql_text
    from information_schema.columns
    where
        table_schema = 'public'
    and table_name = 'categories'
    and column_name like 'slug%';

    if (debug) then raise notice '%', sql_text; end if;

    drop table if exists export_etl.category_slugs cascade;
    execute sql_text;

    alter table export_etl.category_slugs
    add constraint export_etl_category_slugs_pk primary key (id);
end;
$$;

-- test:
-- It should be called from CF with cf_writer as an owner!
-- call export_etl.create_category_slugs(true);
