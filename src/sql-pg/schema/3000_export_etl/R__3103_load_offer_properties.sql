-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.load_offer_properties;
drop procedure if exists export_etl.load_offer_properties;
/**
    Loads analytics.offer_properties from: latform_export.offer_properties view.

    Uses intermediate temp table in order to:
    - minimise lock time on the target caused by truncate;
    - safeguard against data type issues caused during insert
    (that would be caught by the temp table)
*/
create procedure export_etl.load_offer_properties()
language plpgsql as
$$
begin
    -- 1. Load new properties into temp table
    drop table if exists temp_offer_properties;
    create temp table temp_offer_properties (
        like analytics.offer_properties
    ) on commit drop;

    insert into temp_offer_properties (
        offer_id, offer_valid_from, created_at, battery_condition
    )
    select
        offer_id,
        offer_valid_from,
        created_at,
        battery_condition
    from
        export_etl.offer_properties
    ;

    -- 2. Reload target table via insert on conflict update
    insert into analytics.offer_properties(
        offer_id, offer_valid_from, created_at, battery_condition
    )
    select
        offer_id, offer_valid_from, created_at, battery_condition
    from
        temp_offer_properties
    on conflict(offer_id) do update
    set (offer_valid_from, created_at, battery_condition) = (
        excluded.offer_valid_from, excluded.created_at, excluded.battery_condition
    );
end
$$;

-- test: export_etl.load_offer_properties()
-- select * from analytics.offer_properties limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then
        truncate table analytics.offer_properties;
        call export_etl.load_offer_properties();
        perform * from analytics.offer_properties limit 5;
    end if;
end;
$test$;
