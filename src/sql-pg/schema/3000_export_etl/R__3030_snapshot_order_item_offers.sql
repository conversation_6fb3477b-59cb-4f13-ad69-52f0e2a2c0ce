-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.order_item_offers;
create or replace view export_etl.order_item_offers
as
select
    -- order_items: change tracking attributes
    oio.id,
    oio.created_at,
    oio.updated_at,

    -- order_items: business attributes
    oio.order_id,
    oio.offer_id,
    oio.offer_valid_from::timestamp with time zone,
    oio.type,

    -- orders
    oio.paid_at,
    oio.state,
    oio.country,
    oio.presentment_currency,
    oio.payment_provider,

    -- offers: nullable
    platform_export.infinity_to_timestamp(oio.valid_to) as valid_to,
    oio.instance_id,
    oio.merchant_id,
    oio.grading,
    oio.warranty,

    -- offer_properties: nullable
    oio.battery_condition,
    oio.is_new_battery
from
    analytics.order_item_offers as oio;

-- test: select * from platform_export.order_item_offers limit 5;
