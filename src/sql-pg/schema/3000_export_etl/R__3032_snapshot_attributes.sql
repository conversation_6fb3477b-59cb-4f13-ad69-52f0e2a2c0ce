-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.attributes;
create or replace view export_etl.attributes
as
select
    -- PK
    a.id as attribute_id,
    av.id as attribute_value_id,

    -- attributes
    a.attribute_name,
    case
        when a.unit is null
            then
                coalesce(ev.value ->> 'de', av.value_numeric::text)
        else
            coalesce(ev.value ->> 'de', av.value_numeric::text) || ' ' || a.unit
    end::text as attribute_value,
    a.type,
    a.filterable,
    a.precision,
    a.unit,

    -- attribute_values
    av.value_bool,
    av.value_numeric,

    -- attribute_enum_values
    ev.id as value_enum_id,
    ev.value
from
    platform_export.attributes as a
inner join
    platform_export.attribute_values as av
on a.id = av.attribute_id
left join
    platform_export.attribute_enum_values as ev
on av.value_enum_id = ev.id;

-- test: select * from export_etl.attributes limit 5;
