-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists export_etl.full_order_item_refunds;
create view export_etl.full_order_item_refunds
as
with refunds as (
    select
        order_item_refund_id,
        order_item_id,
        order_id,
        created_at,
        updated_at,
        sum(refunded) as refunded,
        sum(case
            when type = 'charge'
                then refunded
        end)::decimal(12, 4) as refunded_charge
    from
        platform_export.order_item_refunds
    where
        status = 'succeeded'
    group by
        order_item_refund_id,
        order_item_id,
        order_id,
        created_at,
        updated_at
),

commission_reversals as (
    select
        r.order_item_refund_id,
        (
            sum(
                coalesce(
                    case
                        when p.status = 'held' and p.type in ('commissionBase', 'addonCommissionBase') then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'held' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )
        ::decimal(12, 4)
            as held_base_commission,
        (
            sum(
                coalesce(
                    case
                        when
                            p.status = 'cancelled' and p.type in ('commissionBase', 'addonCommissionBase')
                            then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'cancelled' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )
        ::decimal(12, 4)
            as kept_base_commission,
        (
            sum(
                coalesce(
                    case
                        when p.status = 'executed' and p.type in ('commissionBase', 'addonCommissionBase') then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'executed' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )::decimal(12, 4
        ) as executed_base_commission,
        (
            sum(coalesce(case when p.status = 'held' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'held' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as held_payment_commission,
        (
            sum(coalesce(case when p.status = 'cancelled' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'cancelled' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as kept_payment_commission,

        (
            sum(coalesce(case when p.status = 'executed' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'executed' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as executed_payment_commission,
        min(
            coalesce(
                case when p.type in ('commissionBase', 'addonCommissionBase') then p.transferred_at end,
                case when s.type in ('commissionBase', 'addonCommissionBase') then sr.reversed_at end
            )
        )::date as min_reversed_commission_date
    from
        platform_export.order_item_refunds as r
    left join
        platform_export.order_transfers_hyperwallet as p
    on r.order_item_refund_id = p.order_item_refund_id
    left join
        platform_export.order_transfer_reversals as sr
    on r.order_item_refund_id = sr.order_item_refund_id
    left join
        platform_export.order_transfers as s
    on sr.order_transfer_id = s.id
    where
        r.status = 'succeeded'
    group by
        r.order_item_refund_id
)

select
    r.order_item_refund_id,
    r.order_item_id,
    r.created_at as refunded_at,
    r.updated_at,
    r.refunded,
    r.refunded_charge,
    (r.refunded * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(12, 4) as refunded_eur,
    (r.refunded_charge * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as refunded_charge_eur,
    cr.min_reversed_commission_date,
    cr.held_base_commission,
    cr.kept_base_commission,
    cr.executed_base_commission,
    cr.held_payment_commission,
    cr.kept_payment_commission,
    cr.executed_payment_commission,
    (cr.held_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as held_base_commission_eur,
    (cr.kept_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as kept_base_commission_eur,
    (cr.executed_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as executed_base_commission_eur,
    (cr.held_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as held_payment_commission_eur,
    (cr.kept_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as kept_payment_commission_eur,
    (cr.executed_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as executed_payment_commission_eur
from
    refunds as r
inner join
    platform_export.orders as o
on r.order_id = o.id
left join
    platform_export.order_items as oi
on r.order_item_id = oi.id
left join
    commission_reversals as cr
on r.order_item_refund_id = cr.order_item_refund_id
left join
    platform_export.exchange_rates as er
on r.created_at >= er.valid_from
    and r.created_at < er.valid_to
    and er.base = 'EUR'
    and o.presentment_currency = er.target;

-- test: select * from export_etl.full_order_item_refunds limit 5;
