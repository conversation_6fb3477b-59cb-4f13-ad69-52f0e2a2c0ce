-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.load_order_item_exchange_rate;
drop procedure if exists export_etl.load_order_item_exchange_rate;
/**
    Loads analytics.order_item_exchange_rate table for:
    - last `reload_days`, defaulted to 14 days;
    - with optional `full_reload`, defaulted to `false`

    Uses intermediate temp table in order to:
    - minimise lock time on the target caused by truncate;
    - safeguard against data type issues caused during insert
    (that would be caught by the temp table)
*/
create procedure export_etl.load_order_item_exchange_rate(
    reload_days int = 14,
    full_reload bool = false
)
language plpgsql as
$$
declare
    reload_date timestamp with time zone;
begin
    -- 0. Initialize reload date
    select
        max(created_at) - concat(reload_days::text, ' day')::interval into reload_date
    from public.order_item_offers;

    -- 1. Create a temp table like the target.
    drop table if exists temp_order_item_exchange_rate;
    create temp table temp_order_item_exchange_rate (
        like analytics.order_item_exchange_rate
    ) on commit drop;

    -- 2. Load data into a temp table, it can take a while, but it does not hurt if it fails.
    insert into temp_order_item_exchange_rate
    select
        order_items.id as order_item_id,
        max(exchange_rates.rate) as exchange_rate
    from
        public.order_item_offers as order_item_offers
    left join
        public.order_items as order_items on order_item_offers.id = order_items.id
    left join
        public.orders as orders on order_items.order_id = orders.id
    left join
        public.exchange_rates as exchange_rates
    on      orders.presentment_currency = exchange_rates.target
        and exchange_rates.base = 'EUR'
        and exchange_rates.valid_to > orders.created_at
        and exchange_rates.valid_from <= orders.created_at
    where
        order_item_offers.created_at >= reload_date
        or full_reload = true
    group by
        order_item_id;

    -- 3. Handle full reload
    if full_reload then
        truncate table analytics.order_item_exchange_rate;
    end if;

    -- 4. Upsert via insert on conflict
    insert into analytics.order_item_exchange_rate
    select * from temp_order_item_exchange_rate
    on conflict(order_item_id) do update set exchange_rate = excluded.exchange_rate;
end
$$;

-- test: select * from analytics.order_item_exchange_rate limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- 379 MB
        -- select pg_size_pretty(pg_relation_size('analytics.order_item_exchange_rate')) as size

        -- test default reload_days
        call export_etl.load_order_item_exchange_rate();

        -- test full_reload
        call export_etl.load_order_item_exchange_rate(full_reload := true);

        perform * from analytics.order_item_exchange_rate limit 5;
    end if;
end;
$test$;
