-- Flyway will re-run this script if it is changed or ${changeReason} is updated
create or replace function platform_export.infinity_to_timestamp(valid_to timestamp without time zone)
returns timestamp with time zone
/*
    Returns `timestamp` value that can be converted into valid `timestamp` in BigQuery.

    Due to pandas limitation we cannot use `9999-12-31`:
    https://calmcode.io/til/pandas-timerange
*/
language sql
immutable
returns null on null input
return
case
    when valid_to = 'infinity'::timestamp without time zone
        then
            '2099-12-31 23:59:59'::timestamp with time zone
    else
        valid_to::timestamp with time zone
end;

/* test:
select
    platform_export.infinity_to_timestamp(null::timestamp without time zone) as as_null,
    platform_export.infinity_to_timestamp('infinity'::timestamp without time zone) as as_infinity,
    platform_export.infinity_to_timestamp(current_timestamp::timestamp without time zone) as as_timestamp;
*/
