-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.shipping_profiles;
create view platform_export.shipping_profiles
as
select
    -- PK: id, valid_from
    id,
    valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(valid_to) as valid_to,
    -- shipping profiles
    merchant_id,
    name::text,
    ships_from::text
from
    public.shipping_profiles;

-- test: select * from platform_export.shipping_profiles limit 5;
