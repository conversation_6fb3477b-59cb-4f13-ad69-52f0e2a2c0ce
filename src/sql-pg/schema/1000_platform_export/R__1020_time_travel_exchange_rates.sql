-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.exchange_rates;
create or replace view platform_export.exchange_rates
as
select
    -- PK / time travel
    t.id,
    t.valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(t.valid_to) as valid_to,
    -- exchange rates
    t.base::text,
    t.target::text,
    t.rate::numeric(24, 9)
from public.exchange_rates as t;

-- test: select * from platform_export.exchange_rates limit 5;
