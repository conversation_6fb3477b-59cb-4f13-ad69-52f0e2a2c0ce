-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.offer_properties cascade;
create or replace view platform_export.offer_properties
as
select
    offer_id,
    offer_valid_from::timestamp with time zone,
    created_at,
    battery_condition,
    coalesce(battery_condition = 'new', false) as is_new_battery
from
    public.offer_properties;

-- test select * from platform_export.offer_properties;
