-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.addons;
create or replace view platform_export.addons
as
select
    -- PK: id, valid_from
    id,
    valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(valid_to) as valid_to,

    -- addons
    type::text,
    name::text,
    payment::text,
    max_qty_per_item,
    instance_id,
    transfer_merchant_id,
    commission_pc::numeric(5, 4)
from
    public.addons;

-- test: select * from platform_export.addons limit 5;
