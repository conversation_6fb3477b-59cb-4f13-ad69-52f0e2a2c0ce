-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.products cascade;
create or replace view platform_export.products
as
select
    id,
    created_at,
    updated_at,
    deleted_at,
    category_id::integer,
    listing_mode::text,
    name::text,
    name_en::text,
    slug::text,
    related_category_ids
from public.products;

-- test: select * from platform_export.products limit 5;
