-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.order_items;
create or replace view platform_export.order_items
as
select
    oi.id,
    oi.created_at,
    oi.updated_at,
    null::timestamp with time zone as deleted_at,

    oi.order_id,
    oi.offer_id,
    oi.offer_valid_from::timestamp with time zone,
    oi.offer_price_id,
    oi.offer_price_valid_from::timestamp with time zone,

    oi.type::text,
    coalesce(oi.type = 'addon', false) as is_addon,
    oi.charged::numeric(14, 4),
    oi.discount::numeric(14, 4),

    oi.net,
    oi.vat::numeric(14, 4),
    oi.vat_country::text,
    oi.price::numeric(14, 4),
    oi.flex_price::numeric(14, 4),

    oi.addon_id,
    oi.addon_valid_from::timestamp with time zone,
    oi.addon_assigned_order_item_id,
    oi.addon_details,

    oi.shipping_profile_destination_id,
    oi.shipping_profile_destination_valid_from::timestamp with time zone,
    oi.shipping_costs::numeric(14, 4),
    oi.is_pre_shipping_versioning,

    oi.presentment_price_gross::numeric(14, 4),
    oi.presentment_flex_price_gross::numeric(14, 4),
    oi.presentment_shipping_costs_gross::numeric(14, 4),

    oi.presentment_base_commission::numeric(14, 4),
    oi.presentment_payment_commission::numeric(14, 4),
    oi.presentment_payout_commission::numeric(14, 4),
    oi.presentment_target_price_commission::numeric(14, 4),
    oi.presentment_commissions::numeric(14, 4),
    oi.presentment_exchange_rate_modifier::numeric(24, 9),
    oi.presentment_to_settlement_exchange_rate::numeric(24, 9),

    oi.settlement_price_gross::numeric(14, 4),
    oi.settlement_flex_price_gross::numeric(14, 4),
    oi.settlement_shipping_costs_gross::numeric(14, 4),
    oi.settlement_currency::text,

    oi.settlement_base_commission::numeric(14, 4),
    oi.settlement_payout_commission::numeric(14, 4),
    oi.settlement_payment_commission::numeric(14, 4),
    oi.settlement_target_price_commission::numeric(14, 4),
    oi.settlement_commissions::numeric(14, 4),

    oi.commission_pc::numeric(5, 4),
    oi.payment_commission_pc::numeric(5, 4),
    oi.payout_commission_pc::numeric(5, 4),
    oi.target_price_commission_pc::numeric(5, 4)
from public.order_items as oi;

-- test: select * from platform_export.order_items limit 5;
