-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.addresses;
create or replace view platform_export.addresses
as
select
    -- PK / time travel
    id,
    valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(valid_to) as valid_to,
    -- Mandatory address fields
    owner_id,
    first_name::text,
    family_name::text,
    country::text,
    post_code::text,
    town::text,
    street_name::text,
    house_no::text,
    phone::text,
    -- Optional address fields
    male,
    address_type::text,
    supplement::text,
    company::text,
    vatin::text,
    personal_vatin::text
from public.addresses;

-- test: select * from platform_export.addresses limit 5;
