-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.instances;
create or replace view platform_export.instances
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,

    published,
    product_id,
    name::text,
    name_en::text,
    srp::numeric(14, 4)
from public.instances;

-- test: select * from platform_export.instances limit 5;
