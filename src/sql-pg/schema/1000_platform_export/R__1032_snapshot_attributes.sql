-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.attributes;
create or replace view platform_export.attributes
as
select
    id,
    name::text as attribute_name,
    type::text,
    filterable,
    precision,
    unit::text
from
    public.attributes;

drop view if exists platform_export.attribute_values;
create or replace view platform_export.attribute_values
as
select
    id,
    attribute_id,
    value_enum_id,
    value_bool,
    value_numeric
from
    public.attribute_values;

drop view if exists platform_export.attribute_enum_values;
create or replace view platform_export.attribute_enum_values
as
select
    id,
    value
from public.attribute_enum_values;

/* test:
select * from platform_export.attributes limit 5;
select * from platform_export.attribute_values limit 5;
select * from platform_export.attribute_enum_values limit 5;
*/
