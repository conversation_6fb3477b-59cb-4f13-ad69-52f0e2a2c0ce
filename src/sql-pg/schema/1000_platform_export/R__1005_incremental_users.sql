-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.users;
create or replace view platform_export.users
as
select
    id,
    created_at,
    updated_at,
    deleted_at,

    email::text,
    first_name::text,
    family_name::text,
    type::text,
    language::text,
    merchant_id
from public.users;

-- test: select * from platform_export.users limit 5;
