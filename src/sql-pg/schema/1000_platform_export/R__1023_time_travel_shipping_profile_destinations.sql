-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.shipping_profile_destinations;
create view platform_export.shipping_profile_destinations
as
select
    -- PK / time travel
    id,
    valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(valid_to) as valid_to,
    -- shipping profile destinations
    shipping_profile_id,
    platform_export.infinity_to_timestamp(shipping_profile_valid_to) as shipping_profile_valid_to,
    country::text,
    shipping_costs::numeric(14, 4),
    delivery_min_days,
    delivery_max_days,
    delivery_time_cond
from
    public.shipping_profile_destinations;

-- test: select * from platform_export.shipping_profile_destinations limit 5;
