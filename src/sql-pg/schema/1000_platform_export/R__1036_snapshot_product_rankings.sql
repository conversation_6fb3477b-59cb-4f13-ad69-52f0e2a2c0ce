-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.product_rankings;
create or replace view platform_export.product_rankings
as
select
    product_id,
    category_id,
    market_country::text,
    -- to be added by platform team in the future
    current_timestamp as updated_at,
    rank,
    rank_b
from public.product_rankings;

/*
select * from platform_export.product_rankings limit 5;
select pg_size_pretty(pg_relation_size('public.product_rankings')) as size;
*/
