-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.categories;
create or replace view platform_export.categories
as
select
    -- PK / time tracking
    c.id,
    c.created_at,
    c.updated_at,
    null::timestamp with time zone as deleted_at,
    -- Category details
    c.name::text as category_name,
    c.name_en::text as product_category,
    c.brand::text,
    c.type::text as category_type,
    -- Category hierarchy
    c.path::text as category_path,
    nlevel(c.path) as category_level,
    case when nlevel(c.path) > 0 then
            subpath(c.path, -1, 1)::text::int
    end as parent_category_id
from
    public.categories as c;

-- test: select * from platform_export.categories limit 5;
