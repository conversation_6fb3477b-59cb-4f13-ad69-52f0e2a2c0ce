-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.merchants;
create or replace view platform_export.merchants
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,

    country::text,
    state::text,
    is_addon_support,
    name::text,
    email::text,
    public_id::text,

    handicap::numeric(5, 4),
    payment_commission_pc::numeric(5, 4),
    payout_commission_pc::numeric(5, 4)
from public.merchants;

-- test: select * from platform_export.merchants limit 5;
