-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.offers;
create or replace view platform_export.offers
as
select
    -- PK / time travel
    o.id,
    o.created_at,
    o.valid_from::timestamp with time zone,
    platform_export.infinity_to_timestamp(o.valid_to) as valid_to,

    -- offers
    o.state::text,
    o.instance_id,
    o.merchant_id,
    o.warranty::smallint,
    o.stock,
    o.grading::text,
    o.hidden,
    o.tax_difference,

    o.shipping_profile_id,
    o.shipping_profile_valid_from::timestamp with time zone,
    o.reference_currency_code::text,
    o.reference_price::numeric(14, 4),
    o.reference_min_flex_price::numeric(14, 4),
    o.sku::text
from
    public.offers as o;

-- test: select * from platform_export.offers limit 5;
