-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists platform_export.order_item_refunds cascade;
create view platform_export.order_item_refunds
as
select
    oir.id as order_item_refund_id,
    oir.order_item_id,
    r.order_id,
    r.created_at,
    r.updated_at,
    oir.refunded::decimal(12, 4),
    r.type::text,
    r.status::text
from
    public.order_item_refunds as oir
inner join
    public.order_refunds as r
on oir.order_refund_id = r.id;

drop view if exists platform_export.order_transfers_hyperwallet cascade;
create view platform_export.order_transfers_hyperwallet
as
select
    id,
    order_id,
    order_refund_id,
    order_item_refund_id,
    type::text,
    status::text,
    transferred_at,
    amount::decimal(12, 4)
from public.order_transfers_hyperwallet;

drop view if exists platform_export.order_transfer_reversals cascade;
create view platform_export.order_transfer_reversals
as
select
    id,
    order_refund_id,
    order_item_refund_id,
    order_transfer_id,
    status::text,
    reversed_at,
    amount::decimal(12, 4),
    presentment_amount::decimal(12, 4),
    stripe_amount::decimal(12, 4)
from public.order_transfer_reversals;

drop view if exists platform_export.order_transfers cascade;
create view platform_export.order_transfers
as
select
    id,
    order_id,
    type::text,
    status::text
from public.order_transfers;

/* test:
select * from platform_export.order_item_refunds limit 5;
select * from platform_export.order_transfers_hyperwallet limit 5;
select * from platform_export.order_transfer_reversals limit 5;
select * from platform_export.order_transfers limit 5;
*/
