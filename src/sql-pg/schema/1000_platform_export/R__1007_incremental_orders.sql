-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists platform_export.orders;
create view platform_export.orders
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,

    paid_at,
    country::text,
    state::text,
    state in ('released', 'released.failed') as is_released,

    user_id,
    partner_id,
    invoice_addr_id,
    invoice_addr_valid_from::timestamp with time zone,
    shipping_addr_id,
    shipping_addr_valid_from::timestamp with time zone,

    presentment_currency::text,
    payment_provider::text,
    payment_info::text,
    payment_handle::text,
    coalesce(
        payment_provider_info -> payment_provider::text ->> 'category',
        payment_provider_info -> 'stripe-digital-wallet' ->> 'type',
        ''
    ) as payment_provider_category,
    payment_provider_info
from public.orders;

-- test: select * from platform_export.orders limit 5;
