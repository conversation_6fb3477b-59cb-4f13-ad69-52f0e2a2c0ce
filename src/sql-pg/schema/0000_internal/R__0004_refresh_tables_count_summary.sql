-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists maintenance.get_refresh_tables_summary;
/**
  Gets refresh tables count summary.
*/
create function maintenance.get_refresh_tables_summary(refresh_run_id text, show_diff_only bool = false)
returns table
(
    schema_name text,
    table_name text,
    platform_db bigint,
    analytics_db bigint,
    diff bigint
)
as
$func$
begin
    drop table if exists refresh_stats;
    create temporary table refresh_stats as
    select
        tc.schema_name,
        tc.table_name,
        tc.db_name,
        tc.table_count
    from
        maintenance.refresh_tables_count tc
    where
        tc.run_id = refresh_run_id;

    drop table if exists refresh_summary;
    create temporary table refresh_summary as
    select
        t.schema_name,
        t.table_name,
        t.platform_db,
        t.analytics_db,
        abs(coalesce(t.platform_db, 0) - coalesce(t.analytics_db, 0)) as diff
     from
        public.crosstab(
                 'select concat(schema_name, table_name) as table_id, schema_name, ' ||
                 'table_name, db_name, table_count ' ||
                 'from refresh_stats ' ||
                 'order by table_id',
                 $$ VALUES ('platform_db'), ('analytics_db') $$
        ) as t (table_id text, schema_name text, table_name text, platform_db bigint, analytics_db bigint);

    return query select
                    rs.*
                  from
                    refresh_summary as rs
                  where
                    (show_diff_only = true and rs.diff > 0) or (show_diff_only=false);

end
$func$ language plpgsql volatile
parallel safe;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from maintenance.get_refresh_tables_summary(refresh_run_id => '123abc') f where diff > 0;
    end if;
end;
$test$;
