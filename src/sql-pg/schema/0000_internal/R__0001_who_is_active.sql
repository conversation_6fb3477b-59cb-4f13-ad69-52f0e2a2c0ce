-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists monitoring.who_is_active;
/**
  Helper function to see the current activity on the instance.
*/
create function monitoring.who_is_active(
    show_own_pid boolean default false,
    show_system_pids boolean default false,
    show_parallel_worker boolean default false,
    show_cloudsql_user boolean default false,
    show_all_pids boolean default false,
    out "hh:mm:ss.msssss" text,
    out session_id integer,
    out sql_text text,
    out parallelism_cnt bigint,
    out login_name name,
    out wait_info text,
    out blocking_session_id integer,
    out status text,
    out host_ip inet,
    out database_name name,
    out program_name text,
    out start_time timestamp with time zone,
    out login_time timestamp with time zone,
    out collection_time timestamp with time zone,
    out duration_sec numeric,
    out backend_type text,
    out is_own_pid bool,
    out is_sleeping_pid bool,
    out is_parallel_worker bool,
    out is_system_pid bool,
    out is_cloudsql_user bool
)
returns setof record
language plpgsql
as
$function$
begin
    return query with blocking as (select
                                       blocked_locks.pid as blocked_pid,
                                       blocked_activity.usename as blocked_user,
                                       blocking_locks.pid as blocking_pid,
                                       blocking_activity.usename as blocking_user,
                                       blocked_activity.query as blocked_statement,
                                       blocking_activity.query as current_statement_in_blocking_process
                                   from
                                       pg_catalog.pg_locks blocked_locks
                                       join pg_catalog.pg_stat_activity blocked_activity
                                       on
                                           blocked_activity.pid = blocked_locks.pid
                                       join pg_catalog.pg_locks blocking_locks
                                       on
                                           blocking_locks.locktype = blocked_locks.locktype
                                               and blocking_locks.database is not distinct from blocked_locks.database
                                               and blocking_locks.relation is not distinct from blocked_locks.relation
                                               and blocking_locks.page is not distinct from blocked_locks.page
                                               and blocking_locks.tuple is not distinct from blocked_locks.tuple
                                               and
                                           blocking_locks.virtualxid is not distinct from blocked_locks.virtualxid
                                               and
                                           blocking_locks.transactionid is not distinct from blocked_locks.transactionid
                                               and blocking_locks.classid is not distinct from blocked_locks.classid
                                               and blocking_locks.objid is not distinct from blocked_locks.objid
                                               and blocking_locks.objsubid is not distinct from blocked_locks.objsubid
                                               and blocking_locks.pid != blocked_locks.pid
                                       join pg_catalog.pg_stat_activity blocking_activity
                                       on
                                           blocking_activity.pid = blocking_locks.pid
                                   where
                                       not blocked_locks.granted),
                      blocking2 as (select distinct
                                        blocked_pid,
                                        blocking_pid
                                    from
                                        blocking),
                      parallelism as (select
                                          datid,
                                          usesysid,
                                          backend_xmin,
                                          count(*) as parallelism_cnt
                                      from
                                          pg_stat_activity
                                      where
                                            datid is not null
                                        and usesysid is not null
                                        and backend_xmin is not null
                                      group by
                                          datid,
                                          usesysid,
                                          backend_xmin
                                      having
                                          count(*) > 1),
                     pg_stat_activities as (
                         select
                             sa.*,
                             -- https://stackoverflow.com/a/64464274/27543394
                             case when sa.backend_type = 'parallel worker' then true else false end is_parallel_worker,
                             case when sa.pid = pg_backend_pid() then true else false end is_own_pid,
                             case when sa.state in ('active', 'idle in transaction') then true else false end is_active_pid,
                             case
                                 when sa.usename in
                                      ('cloudsqladmin',
                                       'cloudsqlagent',
                                       'cloudsqlimportexport',
                                       'cloudsqlobservability',
                                       'cloudsqlreplica',
                                       'cloudsqlsuperuser'
                                       )
                                       then true else false end is_cloudsql_user,
                             now() as collection_time,
                             case when
                                 sa.backend_type in
                                 ('autovacuum launcher',
                                  'autovacuum worker',
                                  'logical replication launcher',
                                  'logical replication worker',
                                  'background writer',
                                  'checkpointer',
                                  'archiver',
                                  'startup',
                                  'walreceiver',
                                  'walsender',
                                  'walwriter'
                                 )
                                 then true else false end as is_system_pid
                         from
                             pg_stat_activity sa
                     )
                 select
                     replace(age(now(), sa.state_change)::text, 'day', '') as "hh:mm:ss.msssss",
                     sa.pid as session_id,
                     sa.query as sql_text,
                     p.parallelism_cnt as parallelism_cnt,
                     sa.usename as login_name,
                     sa.wait_event_type || '(' || sa.wait_event || ')' as wait_info,
                     b.blocking_pid as blocking_session_id,
                     sa.state as status,
                     sa.client_addr as host_ip,
                     sa.datname as database_name,
                     sa.application_name as program_name,
                     sa.state_change as start_time,
                     sa.backend_start as login_time,
                     sa.collection_time,
                     extract(epoch from (sa.collection_time - sa.state_change)) as duration_sec,
                     sa.backend_type,
                     sa.is_own_pid,
                     sa.is_active_pid,
                     sa.is_parallel_worker,
                     sa.is_system_pid,
                     sa.is_cloudsql_user
                 from
                     pg_stat_activities sa
                     left join
                         blocking2 b
                     on  sa.pid = b.blocked_pid
                     left join
                         parallelism p
                     on  p.datid = sa.datid
                     and p.usesysid = sa.usesysid
                     and p.backend_xmin = sa.backend_xmin
                 where
                     (sa.is_own_pid = false or show_own_pid)
                 and (sa.is_active_pid = true or show_all_pids)
                 and (sa.is_parallel_worker = false or show_parallel_worker)
                 and (sa.is_system_pid = false or show_system_pids)
                 and (sa.is_cloudsql_user = false or show_cloudsql_user)
                 order by
                     age(now(), state_change) desc;
end;
$function$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from monitoring.who_is_active(
        show_own_pid => true,
        show_cloudsql_user => true,
        show_system_pids => true,
        show_parallel_worker => true,
        show_all_pids => true) limit 10;

        perform * from monitoring.who_is_active() limit 10;
    end if;
end;
$test$;
