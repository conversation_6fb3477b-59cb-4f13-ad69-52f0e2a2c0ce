-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.configure_read_write_role;
/**
  Helper procedure to configure the read_only and read_write roles.
  It is compatible with Postgres 14+.
*/
create procedure maintenance.configure_read_write_role(
    role_name name, debug bool = false,
    variadic schemas text []
    = array[
        'analytics',
        'monitoring',
        'maintenance',
        'log',
        'import',
        'public',
        'export_etl',
        'platform_export',
        'zendesk'
    ]
)
language plpgsql as
$$
declare
    grant_sql   text := 'select';
    grant_usage text := 'usage';
    grant_pg_role text := 'pg_read_all_data';
    sql         text;
    schema_name text;
    rec         record;
begin
    if role_name not in ('read_only', 'read_write') then
        raise notice 'You can only use "read_only" and "read_write" role but you specified "%"! Stopping.', role_name;
        return;
    end if;

    if role_name = 'read_write' then
        grant_sql := 'select, insert, update, delete, truncate, references';
        grant_usage := 'all';
        grant_pg_role := 'pg_read_all_data, pg_write_all_data';
    end if;

    -- create role if not exists
    if not exists (select * from pg_catalog.pg_roles where rolname = role_name) then
        sql := format('create role %s;', role_name);
        if (debug) then raise notice '%', sql; end if;
        execute sql;
    end if;

    sql := format('grant connect on database %s to %s;', current_database(), role_name);
    if (debug) then raise notice '%', sql; end if;
    execute sql;

    -- grant on schemas
    foreach schema_name in array schemas
        loop
            sql := format('grant %s on schema %s to "%s";', grant_usage, schema_name, role_name);
            if (debug) then raise notice '%', sql; end if;
            execute sql;
        end loop;

    -- grant pg predefined reader/writer role
    sql := format('grant %s to "%s";', grant_pg_role, role_name);
    if (debug) then raise notice '%', sql; end if;
    execute sql;

    -- grant on tables and views
    for rec in select
                   table_schema,
                   table_name
               from
                   information_schema.tables
               where
                   table_schema in (select unnest(schemas))
               order by
                   table_schema,
                   table_type, -- BASE TABLE, VIEW
                   table_name
        loop
            sql := format('grant %s on %s.%s to %s;', grant_sql, rec.table_schema, rec.table_name, role_name);
            if (debug) then raise notice '%', sql; end if;
            execute sql;
        end loop;

    -- grant on functions and procedures
    for rec in select distinct
                    isr.routine_type,
                    isr.routine_schema,
                    isr.routine_name,
                    pg_get_function_identity_arguments(pgp.oid) as args
                from
                    pg_catalog.pg_proc pgp
                inner join
                     pg_catalog.pg_namespace pgn
                on pgp.pronamespace = pgn.oid
                inner join
                     information_schema.routines isr
                on  isr.routine_name = pgp.proname
                and isr.routine_schema = pgn.nspname
               where
                     isr.routine_schema in (select unnest(schemas))
                 and isr.routine_type in ('FUNCTION', 'PROCEDURE')
               order by
                   isr.routine_schema,
                   isr.routine_name
        loop
            if rec.routine_type = 'FUNCTION' then
                sql := format('grant execute on function "%s"."%s"(%s) to %s;', rec.routine_schema, rec.routine_name, rec.args,
                              role_name);
                if (debug) then raise notice '%', sql; end if;
                execute sql;
            end if;

            if rec.routine_type = 'PROCEDURE' then
                -- grant on routines for read_write
                if role_name = 'read_write' then
                    sql := format('grant execute on routine "%s"."%s"(%s) to %s;', rec.routine_schema, rec.routine_name, rec.args,
                                  role_name);
                    if (debug) then raise notice '%', sql; end if;
                    execute sql;
                end if;
            end if;
        end loop;
end
$$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        call maintenance.configure_read_write_role('foo');
        call maintenance.configure_read_write_role( 'read_only',  true);
        call maintenance.configure_read_write_role( 'read_write',  true);
    end if;
end;
$test$;
