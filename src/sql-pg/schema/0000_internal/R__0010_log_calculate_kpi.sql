-- https://www.percentage-change-calculator.com/
drop function if exists log.calculate_kpi;
create function log.calculate_kpi(numerator numeric(15, 2), denominator numeric(15, 2)) returns numeric(15, 2)
language plpgsql
volatile
returns null on null input
as
$$
begin
    if (denominator > 0) then
        return round((numerator - denominator) / denominator * 100, 2);
    else
        return 0;
    end if;

end
$$;


/*
select log.calculate_kpi(3.4, 0)
select log.calculate_kpi(3.4, 5)
select log.calculate_kpi(7.2, 5.7)
select log.calculate_kpi(1.3, 50.7)
select log.calculate_kpi(130.3, 0.7)
 */

drop function if exists log.calculate_kpi_indicator;
create function log.calculate_kpi_indicator(numerator numeric, denominator numeric)
returns char(10)
language plpgsql
volatile
returns null on null input
as
$$
declare
    indicator char(3);
    kpi       numeric(15, 2);
begin

    select log.calculate_kpi(numerator, denominator) into kpi;
    if (kpi = 0) then
        indicator = '■ ';
    elseif (kpi > 0) then
        indicator = '▲ ';
    else
        indicator = '▼ ';
    end if;

    return concat(indicator, lpad(kpi::text, 7), '%');
end
$$;

/*
select log.calculate_kpi_indicator(3.4, 0);
select log.calculate_kpi_indicator(3.4, 5);
select log.calculate_kpi_indicator(7.2, 5.7);
select log.calculate_kpi_indicator(1.3, 50.7);
select log.calculate_kpi_indicator(130.3, 0.7);
 */

drop function if exists log.calculate_duration_from_seconds;
create function log.calculate_duration_from_seconds(seconds numeric) returns varchar(10)
as
$$
select to_char((seconds || ' second')::interval, 'HH24:MI:SS');
$$
language sql
immutable
returns null on null input;

/*
select log.calculate_duration_from_seconds(12345.55)
 */


drop function if exists log.calculate_diff_seconds;
create function log.calculate_diff_seconds(
    started timestamp with time zone, finished timestamp with time zone
) returns numeric
as
$$
select round(abs(extract(epoch from finished - started)))::numeric as seconds;
$$
language sql
immutable
returns null on null input;

/*
select log.calculate_diff_seconds(now() at time zone 'utc' - interval '3 hours 15 minutes 10 seconds',
now() at time zone 'utc')
select log.calculate_diff_seconds(now() at time zone 'utc',
now() at time zone 'utc' - interval '3 hours 15 minutes 10 seconds')
 */

drop function if exists log.calculate_duration;
create function log.calculate_duration(
    start_date timestamp with time zone, end_date timestamp with time zone
) returns varchar(10)
as
$$
select log.calculate_duration_from_seconds(log.calculate_diff_seconds(start_date, end_date))
$$
language sql
immutable
returns null on null input;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform log.calculate_duration(now() at time zone 'utc' - interval '3 hours 15 minutes 10 seconds',
        now() at time zone 'utc');
        perform log.calculate_duration(now() at time zone 'utc',
        now() at time zone 'utc' - interval '3 hours 15 minutes 10 seconds');
    end if;
end;
$test$;
