-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.addresses;
create view ingestion.addresses
as
select
    -- PK / time travel
    a.id,
    a.valid_from,
    a.valid_from::timestamp with time zone as bq_valid_from,
    a.valid_to,
    ingestion.infinity_to_timestamp(a.valid_to) as bq_valid_to,

    -- Mandatory address fields
    a.owner_id,
    a.first_name::text,
    a.family_name::text,
    a.country::text,
    a.post_code::text,
    a.town::text,
    a.street_name::text,
    a.house_no::text,
    a.phone::text,

    -- Optional address fields
    a.male,
    a.address_type::text,
    a.supplement::text,
    a.company::text,
    a.vatin::text,
    a.personal_vatin::text
from public.addresses as a;

-- test: select * from ingestion.addresses limit 5;
