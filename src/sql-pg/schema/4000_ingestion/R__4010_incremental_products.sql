-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.products cascade;
create view ingestion.products
/*
    Product and all its unnested categories where it can be shown/listed.
*/
as
select
    p.id as product_id,
    p.category_id as main_category_id,
    unnest(array[p.category_id::text] || string_to_array(c.category_path, '.'))::integer as show_category_id
from
    platform_export.products as p
inner join
    platform_export.categories as c
on p.category_id = c.id

union

select
    p.id as product_id,
    p.category_id as main_category_id,
    show_category_id
from
    platform_export.products as p,
    unnest(p.related_category_ids::integer []) as show_category_id
where
    show_category_id is not null;

-- test: select * from export_etl.product_categories limit 5;
/*
    -- PK: product_id, show_category_id
    select product_id, show_category_id, count(*) as duplicate_rows
    from ingestion.product_categories
    group by product_id, show_category_id
    having count(*) > 1;
*/
