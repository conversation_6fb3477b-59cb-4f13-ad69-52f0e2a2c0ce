-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.addons;
create view ingestion.addons
as
select
    -- PK: id, valid_from
    a.id,
    a.valid_from,
    a.valid_from::timestamp with time zone as bq_valid_from,
    a.valid_to,
    ingestion.infinity_to_timestamp(a.valid_to) as bq_valid_to,

    -- addons
    a.type::text,
    a.name::text,
    a.payment::text,
    a.max_qty_per_item,
    a.instance_id,
    a.transfer_merchant_id,
    a.commission_pc::numeric(5, 4)
from
    public.addons as a;

-- test: select * from ingestion.addons limit 5;
