-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.orders;
create view ingestion.orders
as
select
    -- PK / time tracking
    o.id,
    o.created_at,
    o.updated_at,
    null::timestamp with time zone as deleted_at,

    o.paid_at,
    o.country::text,
    o.state::text,
    o.state in ('released', 'released.failed') as is_released,

    o.user_id,
    o.partner_id,
    o.invoice_addr_id,
    o.invoice_addr_valid_from::timestamp with time zone,
    o.shipping_addr_id,
    o.shipping_addr_valid_from::timestamp with time zone,

    o.presentment_currency::text,
    o.payment_provider::text,
    o.payment_info::text,
    o.payment_handle::text,
    coalesce(
        o.payment_provider_info -> o.payment_provider::text ->> 'category',
        o.payment_provider_info -> 'stripe-digital-wallet' ->> 'type',
        ''
    ) as payment_provider_category,
    o.payment_provider_info
from public.orders as o;

-- test: select * from ingestion.orders limit 5;
