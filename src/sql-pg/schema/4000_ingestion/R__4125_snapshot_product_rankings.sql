-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.product_rankings;
create view ingestion.product_rankings
as
select
    pr.product_id,
    pr.category_id,
    pr.market_country::text,
    -- to be added by platform team in the future
    current_timestamp as updated_at,
    pr.rank,
    pr.rank_b
from public.product_rankings as pr;

--select * from ingestion.product_rankings limit 5;
