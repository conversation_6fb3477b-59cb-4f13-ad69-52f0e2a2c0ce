-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.merchants;
create view ingestion.merchants
as
select
    -- PK / time tracking
    m.id,
    m.created_at,
    m.updated_at,
    null::timestamp with time zone as deleted_at,

    m.country::text,
    m.state::text,
    m.is_addon_support,
    m.name::text,
    m.email::text,
    m.public_id::text,

    m.handicap::numeric(5, 4),
    m.payment_commission_pc::numeric(5, 4),
    m.payout_commission_pc::numeric(5, 4)
from public.merchants as m;

-- test: select * from ingestion.merchants limit 5;
