-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists ingestion.order_item_refunds;
create view ingestion.order_item_refunds
as
select
    oir.id as order_item_refund_id,
    oir.order_item_id,
    r.order_id,
    r.created_at,
    r.updated_at,
    oir.refunded::numeric(14, 4),
    r.type::text,
    r.status::text
from
    public.order_item_refunds as oir
inner join
    public.order_refunds as r
on oir.order_refund_id = r.id;


/* test:
select * from ingestion.order_item_refunds limit 5;
*/
