-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists ingestion.order_transfers_hyperwallet;
create view ingestion.order_transfers_hyperwallet
as
select
    oth.id,
    oth.order_id,
    oth.order_refund_id,
    oth.order_item_refund_id,
    oth.type::text,
    oth.status::text,
    oth.transferred_at,
    oth.amount::numeric(14, 4)
from public.order_transfers_hyperwallet as oth;

/* test:
select * from ingestion.order_transfers_hyperwallet limit 5;
*/
