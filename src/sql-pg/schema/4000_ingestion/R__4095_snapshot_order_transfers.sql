-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists ingestion.order_transfers;
create view ingestion.order_transfers
as
select
    ot.id,
    ot.order_id,
    ot.type::text,
    ot.status::text,
    ot.transferred_at,
    ot.amount::numeric(14, 4),
    ot.presentment_amount::numeric(14, 4),
    ot.stripe_amount::numeric(14, 4)
from public.order_transfers as ot;

/* test:
select * from ingestion.order_transfers limit 5;
*/
