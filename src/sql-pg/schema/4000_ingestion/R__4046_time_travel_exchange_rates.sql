-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.exchange_rates;
create view ingestion.exchange_rates
as
select
    -- PK / time travel
    t.id,
    t.valid_from,
    t.valid_from::timestamp with time zone as bq_valid_from,
    t.valid_to,
    ingestion.infinity_to_timestamp(t.valid_to) as bq_valid_to,

    -- exchange rates
    t.base::text,
    t.target::text,
    t.rate::numeric(24, 9)
from public.exchange_rates as t;

-- test: select * from ingestion.exchange_rates limit 5;
