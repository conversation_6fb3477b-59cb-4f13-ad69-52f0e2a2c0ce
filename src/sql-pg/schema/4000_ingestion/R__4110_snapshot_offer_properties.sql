-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.offer_properties cascade;
create view ingestion.offer_properties
as
select
    op.offer_id,
    op.offer_valid_from::timestamp with time zone,
    op.created_at,
    op.battery_condition,
    coalesce(op.battery_condition = 'new', false) as is_new_battery
from
    public.offer_properties as op;

-- test select * from ingestion.offer_properties;
