-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists ingestion.order_transfer_reversals;
create view ingestion.order_transfer_reversals
as
select
    otr.id,
    otr.order_refund_id,
    otr.order_item_refund_id,
    otr.order_transfer_id,
    otr.status::text,
    otr.reversed_at,
    otr.amount::numeric(14, 4),
    otr.presentment_amount::numeric(14, 4),
    otr.stripe_amount::numeric(14, 4)
from public.order_transfer_reversals as otr;


/* test:
select * from ingestion.order_transfer_reversals limit 5;
*/
