-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists ingestion.infinity_to_timestamp;
create function ingestion.infinity_to_timestamp(valid_to timestamp without time zone)
returns timestamp with time zone
/*
    Returns `timestamp` value that can be converted into valid `timestamp` in BigQuery.
*/
language sql
immutable
returns null on null input
return
case
    when valid_to = 'infinity'::timestamp without time zone
        then
            '2099-12-31 23:59:59'::timestamp with time zone at time zone 'UTC'
    else
        valid_to::timestamp with time zone
end;

/* test:
select
    ingestion.infinity_to_timestamp(null::timestamp without time zone) as as_null,
    ingestion.infinity_to_timestamp('infinity'::timestamp without time zone) as as_infinity,
    ingestion.infinity_to_timestamp(current_timestamp::timestamp without time zone) as as_timestamp;
*/
