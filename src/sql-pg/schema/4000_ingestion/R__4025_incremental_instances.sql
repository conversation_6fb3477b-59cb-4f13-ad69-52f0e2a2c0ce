-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.instances;
create view ingestion.instances
as
select
    -- PK / time tracking
    i.id,
    i.created_at,
    i.updated_at,
    null::timestamp with time zone as deleted_at,

    i.published,
    i.product_id,
    i.name::text,
    i.name_en::text,
    i.srp::numeric(14, 4)
from public.instances as i;

-- test: select * from ingestion.instances limit 5;
