-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.shipping_profiles;
create view ingestion.shipping_profiles
as
select
    -- PK: id, valid_from
    sp.id,
    sp.valid_from,
    sp.valid_from::timestamp with time zone as bq_valid_from,
    sp.valid_to,
    ingestion.infinity_to_timestamp(sp.valid_to) as bq_valid_to,

    -- shipping profiles
    sp.merchant_id,
    sp.name::text,
    sp.ships_from::text
from
    public.shipping_profiles as sp;

-- test: select * from ingestion.shipping_profiles limit 5;
