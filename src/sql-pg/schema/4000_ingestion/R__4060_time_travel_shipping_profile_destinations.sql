-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.shipping_profile_destinations;
create view ingestion.shipping_profile_destinations
as
select
    -- PK / time travel
    spd.id,
    spd.valid_from,
    spd.valid_from::timestamp with time zone as bq_valid_from,
    spd.valid_to,
    ingestion.infinity_to_timestamp(spd.valid_to) as bq_valid_to,

    -- shipping profile destinations
    spd.shipping_profile_id,
    ingestion.infinity_to_timestamp(spd.shipping_profile_valid_to) as shipping_profile_valid_to,
    spd.country::text,
    spd.shipping_costs::numeric(14, 4),
    spd.delivery_min_days,
    spd.delivery_max_days,
    spd.delivery_time_cond
from
    public.shipping_profile_destinations as spd;

-- test: select * from ingestion.shipping_profile_destinations limit 5;
