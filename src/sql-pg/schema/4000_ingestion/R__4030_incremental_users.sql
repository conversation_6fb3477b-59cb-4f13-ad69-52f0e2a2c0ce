-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.users;
create view ingestion.users
as
select
    -- PK / time tracking
    u.id,
    u.created_at,
    u.updated_at,
    u.deleted_at,

    u.email::text,
    u.first_name::text,
    u.family_name::text,
    u.full_name::text,
    u.type::text,
    u.language::text,
    u.merchant_id,
    u.partner_id
from public.users as u;

-- test: select * from ingestion.users limit 5;
