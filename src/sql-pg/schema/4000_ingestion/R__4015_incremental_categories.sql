-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists ingestion.categories;
create view ingestion.categories
as
-- Root categories
select
    c.id,
    c.created_at,
    c.updated_at,
    c.deleted_at,

    c.category_name,
    c.brand,
    c.category_type,
    c.product_category
from
    platform_export.categories as c
where
    c.category_level = 0

union all

-- Subcategories
select
    c.id,
    c.created_at,
    c.updated_at,
    c.deleted_at,
    c.category_name,
    c.brand,
    c.category_type,
    pc.product_category
from
    platform_export.categories as c
left join
    platform_export.categories as pc
on c.parent_category_id = pc.id
where
    c.category_level > 0;


-- test: select * from ingestion.categories limit 5;
