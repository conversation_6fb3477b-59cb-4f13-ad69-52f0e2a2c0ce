"""
Backmarket/Algolia API interface.
Enables getting data directly from API instead of parsing HTML page
"""

from copy import deepcopy
from dataclasses import dataclass, field
from enum import StrEnum
from http import HTT<PERSON>ethod
from typing import Optional

from bm_shared.oxy_http_client import OxyHTTPClient
from dataclasses_json import DataClassJsonMixin, LetterCase, config


class ApiParsingError(Exception):
    """Error during parsing API response"""


type ApiPayload = dict[str, str | bool | list[str] | int]


@dataclass(frozen=True)
class AlgoliaHash(DataClassJsonMixin):
    offer_type: Optional[str] = field(metadata=config(letter_case=LetterCase.CAMEL), default=None)


@dataclass(frozen=True)
class AlgoliaLinkGradeV2(DataClassJsonMixin):
    href: str
    hash: AlgoliaHash


@dataclass(frozen=True)
class AlgoliaHit(DataClassJsonMixin):
    backmarket_id: str = field(metadata=config(field_name="backmarketID"))
    id: str
    title_model: str
    title: str
    sub_title_elements: list[str]
    backbox_grade_label: str
    link_grade_v2: AlgoliaLinkGradeV2
    price: float
    currency: str
    merchant_id: int

    @property
    def is_battery_new(self) -> bool:
        return self.link_grade_v2.hash.offer_type == ApiParams.NEW_BATTERY_OFFER_TYPE


@dataclass(frozen=True)
class AlgoliaParsedResponse(DataClassJsonMixin):
    nb_hits: int = field(metadata=config(letter_case=LetterCase.CAMEL))
    hits: list[AlgoliaHit]


class ApiHeaders:
    X_ALGOLIA_API_KEY: str = "x-algolia-api-key"
    X_ALGOLIA_APPLICATION_ID: str = "x-algolia-application-id"
    REFERER: str = "Referer"
    ORIGIN: str = "Origin"

    HEADERS: dict[str, str] = {
        "Accept": "*/*",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        ORIGIN: "https://www.backmarket.{}",
        "Pragma": "no-cache",
        REFERER: "https://www.backmarket.{}/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "cross-site",
        "content-type": "application/x-www-form-urlencoded",
        X_ALGOLIA_API_KEY: "",
        X_ALGOLIA_APPLICATION_ID: "",
    }


class ApiParams:
    INSTANCE_SEPARATOR: str = " | "
    PRODUCTS_PER_PAGE: int = 32
    API_URL: str = (
        "https://search.backmarket.{}/1/indexes/prod_index_backbox_{}/query?"
        "x-algolia-agent=Algolia%20for%20JavaScript%20(4.24.0)%3B%20Browser"
    )
    NEW_BATTERY_OFFER_TYPE: str = "7"
    NEW_BATTERY: str = "New Battery"


@dataclass(frozen=True)
class ApiFilterAttribute:
    key: str
    value: str

    @property
    def to_text(self) -> str:
        return f'({self.key}:"{self.value}")'


class ApiFilterOperator(StrEnum):
    AND = "AND"
    OR = "OR"


class ApiFilter:
    """Algolia filter builder"""

    BACKBOX_GRADE = "backbox_grade"

    @staticmethod
    def prepend_attribute(
        attribute: ApiFilterAttribute, base_query: str, operator: ApiFilterOperator = ApiFilterOperator.AND
    ) -> str:
        return f"{attribute.to_text} {operator.value} {base_query}"


class ApiGrade:
    FILTERS: tuple[tuple[str, str], ...] = (
        # https://www.backmarket.at/de-at/p/iphone-13-128-gb-ohne-vertrag/ec975bb8-df95-43a5-b04d-de63378f4a12?l=10#
        ("de-at", "12 Gut"),
        ("de-at", "11 Sehr gut"),
        ("de-at", "10 Hervorragend"),
        ("de-at", "9 Premium"),
        # https://www.backmarket.de/de-de/p/iphone-13-128-gb-ohne-vertrag/ef5660d2-6883-4b81-b47d-86e5720687ef?l=12
        ("de-de", "12 Gut"),
        ("de-de", "11 Sehr gut"),
        ("de-de", "10 Hervorragend"),
        ("de-de", "9 Premium"),
        # https://www.backmarket.it/it-it/p/iphone-13-128-go-compatibile-con-tutti-gli-operatori/ef5660d2-6883-4b81-b47d-86e5720687ef?l=10 # noqa
        ("it-it", "12 Buono"),
        ("it-it", "11 Ottimo"),
        ("it-it", "10 Eccellente"),
        ("it-it", "9 Premium"),
        # https://www.backmarket.se/sv-se/p/iphone-13-128-gb-rosa-olast/d1989098-648d-490e-a719-7c4b6ab8b1ae?l=11#
        ("sv-se", "12 Bra"),
        ("sv-se", "11 Mycket bra"),
        ("sv-se", "10 Utmärkt"),
        ("sv-se", "9 Premium"),
        # https://www.backmarket.nl/nl-nl/p/iphone-13-128-gb-simlockvrij/ec975bb8-df95-43a5-b04d-de63378f4a12?l=10#
        ("nl-nl", "12 Goed"),
        ("nl-nl", "11 Heel goed"),
        ("nl-nl", "10 Uitstekend"),
        ("nl-nl", "9 Premium"),
        # https://www.backmarket.pt/pt-pt/p/iphone-13-128-gb-desbloqueado/ef5660d2-6883-4b81-b47d-86e5720687ef?l=12
        ("pt-pt", "12 Correto"),
        ("pt-pt", "11 Bom"),
        ("pt-pt", "10 Excelente"),
        ("pt-pt", "9 Premium"),
        # https://www.backmarket.be/fr-be/p/iphone-13-128-go-minuit-debloque-tout-operateur/ef5660d2-6883-4b81-b47d-86e5720687ef?l=12  # noqa
        ("fr-be", "12 État correct"),
        ("fr-be", "11 Très bon état"),
        ("fr-be", "10 Parfait état"),
        ("fr-be", "9 Premium"),
        # https://www.backmarket.ie/en-ie/p/iphone-13-128-gb-unlocked/ec975bb8-df95-43a5-b04d-de63378f4a12?l=11
        ("en-ie", "12 Fair"),
        ("en-ie", "11 Good"),
        ("en-ie", "10 Excellent"),
        ("en-ie", "9 Premium"),
    )
    GRADES_PER_LOCALE = 4


class ApiPayloadParams:

    FILTERS: str = "filters"
    PAGE: str = "page"

    IPHONE_PAYLOAD: ApiPayload = {
        "query": "",
        "distinct": 1,
        "clickAnalytics": True,
        FILTERS: "",
        "facets": [
            "price",
            "page",
            "q",
            "sort",
            "storage",
            "color",
            "sim_lock",
            "backbox_grade",
            "real_screen_size",
            "warranty_with_unit",
            "payment_methods",
            "shipping_delay",
            "price_ranges.sm-1",
            "price_ranges.sm-2",
            "price_ranges.md-1",
            "price_ranges.md-1b",
            "price_ranges.md-1c",
            "price_ranges.md-2",
            "price_ranges.lg-1",
            "price_ranges.lg-2",
            "price_ranges.lg-3",
        ],
        PAGE: 0,
        "hitsPerPage": ApiParams.PRODUCTS_PER_PAGE,
    }


class AlgoliaClient:
    def __init__(self, oxy_client: OxyHTTPClient):
        self._oxy_client = oxy_client

    @staticmethod
    def payload(payload_template: ApiPayload, query_filter: str, page: int) -> ApiPayload:
        """Build payload for Algolia API call

        :param payload_template: template with output fields
        :param query_filter: request filter clause
        :param page: page number to be retrieved
        :return: payload
        """
        json_payload = deepcopy(payload_template)
        json_payload[ApiPayloadParams.FILTERS] = query_filter
        json_payload[ApiPayloadParams.PAGE] = page
        return json_payload

    @staticmethod
    def parse_response(response_content: str) -> AlgoliaParsedResponse:
        """parse Algolia API response

        :param response_content: serialized json response
        :raises ValidationError: invalid response without product name, price , etc.
        :return: dataclass with product data , total number of hits per query
        """
        return AlgoliaParsedResponse.from_json(response_content)

    def get_hits(self, payload: ApiPayload, language_country: str) -> str:
        """request Algolia Backmarket response

        :param payload: input including filters
        :param language_country: language and country info e.g. de_at
        :return: Algolia response content
        """
        url = self._url(language_country)
        headers = self._headers(language_country)
        api_response = self._oxy_client.get_page(
            target_url=url, headers=headers, method=HTTPMethod.POST, json_data=payload
        )
        return api_response

    @staticmethod
    def _url(language_country: str) -> str:
        """Build URL for Algolia API call

        :return: url
        """
        return ApiParams.API_URL.format(language_country.split("-")[1], language_country)

    @staticmethod
    def country_code(language_country: str) -> str:
        """get two-letter country code from language-country property e.g. 'de-at'-> 'at'

        :param language_country: language-country tag e.g. 'de-at'
        :return: country code
        """
        return language_country.split("-")[-1]

    @staticmethod
    def _headers(language_country: str) -> dict[str, str]:
        """Build HTTP header for Algolia API call

        :param language_country: language-country tag e.g. 'de-at'
        :return: headers
        """
        headers = deepcopy(ApiHeaders.HEADERS)
        headers[ApiHeaders.X_ALGOLIA_API_KEY] = "dummy_key"
        headers[ApiHeaders.X_ALGOLIA_APPLICATION_ID] = "dummy_app_id"
        headers[ApiHeaders.REFERER] = headers[ApiHeaders.REFERER].format(AlgoliaClient.country_code(language_country))
        headers[ApiHeaders.ORIGIN] = headers[ApiHeaders.ORIGIN].format(AlgoliaClient.country_code(language_country))
        return headers
