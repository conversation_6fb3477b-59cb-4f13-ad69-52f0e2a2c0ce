from dataclasses import dataclass

from pandas import DataFrame
from tenacity import (
    Retry<PERSON>allState,
    Retrying,
    stop_after_attempt,
    wait_random_exponential,
)

from common.logger import get_logger

logger = get_logger()

# 15 attempts provides good resilience for transient network issues and API rate limits
REQUEST_RETRY_ATTEMPTS: int = 15
# 3 attempts is sufficient for message polling as empty responses are common
RECEIVE_RETRY_ATTEMPTS: int = 3
ATTEMPT: str = "attempt_number"


def _log_retry_attempt(retry_state: RetryCallState) -> None:
    """Log retry attempt with concise error details"""
    attempt = retry_state.attempt_number
    exception = retry_state.outcome.exception()
    wait_time = getattr(retry_state, "next_action", None)
    wait_seconds = wait_time.sleep if wait_time else 0

    logger.info(
        f"Retry attempt {attempt}/{REQUEST_RETRY_ATTEMPTS} failed: "
        f"{type(exception).__name__}: {str(exception)[:100]}... "
        f"(waiting {wait_seconds:.1f}s before retry)"
    )


# Exponential backoff with random jitter: 3s base, 2x each attempt, random variation, max 15s (total ~1-2min)
RETRIER = Retrying(
    wait=wait_random_exponential(min=3, max=15, multiplier=2),
    stop=stop_after_attempt(REQUEST_RETRY_ATTEMPTS),
    reraise=True,
    before_sleep=_log_retry_attempt,
)


@dataclass(frozen=True)
class Loaded:
    records: int
    requests: int


@dataclass(frozen=True)
class RequestTiming:
    api_call: float
    save_extracted: float
    transform: float
    republish_subsequent_requests: float
    records: int
    subsequent_requests: int


@dataclass(frozen=True)
class ProcessedRequest:
    transformed: DataFrame
    timing: RequestTiming
