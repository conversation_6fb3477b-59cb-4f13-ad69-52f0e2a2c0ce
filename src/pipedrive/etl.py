import os
from functools import cached_property
from pathlib import Path
from typing import Callable, Optional, TypeAlias

from pandas import DataFrame
from pipedrive_api_client import PipedriveApiClient
from pipedrive_bq_repo import PipedriveBqRepository
from pipedrive_model import (
    PIPEDRIVE_DATA_LAKE_PARTITION,
    PipedriveConfig,
    PipedriveExtracted,
    PipedriveSchema,
    PipedriveTransformed,
)
from pipedrive_pg_repo import PipedrivePgRepository
from pipedrive_transformer import PipedriveTransformer

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.monitoring import profile
from common.pandas_utils import DataFrameWithSchema
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository
from common.utils import format_bytes_pretty

logger = get_logger()

ExtractedType: Type<PERSON>lias = PipedriveExtracted
TransformedType: TypeAlias = list[PipedriveTransformed]
LoadedType: TypeAlias = tuple[int, int, int]
TransformerCreatorType: TypeAlias = Callable[[list[Path]], PipedriveTransformer]


class PipedriveEtl(BaseEtl, SupportsPipelineExecution):
    _config: PipedriveConfig

    def __init__(
        self,
        config: PipedriveConfig,
        api_client: PipedriveApiClient,
        transformer_creator: TransformerCreatorType,
        local_temp_path: Path,
        raw_data_lake_repository: DataLakeRepository,
        bq_repository: PipedriveBqRepository,
        pg_repository: PipedrivePgRepository,
    ) -> None:
        super().__init__(config)
        self._api_client = api_client
        self._transformer_creator = transformer_creator
        self._local_temp_path = local_temp_path
        self._raw_data_lake_repository = raw_data_lake_repository
        self._bq_repository = bq_repository
        self._pg_repository = pg_repository

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._pg_repository

    @cached_property
    def run_suffix(self) -> str:
        """Run partition suffix"""
        return f"run_id={self.run_id}"

    @cached_property
    def local_path(self) -> Path:
        """Temporary local path where data will be downloaded"""
        return Path(self._local_temp_path, self.run_suffix)

    @cached_property
    def data_lake_path(self) -> Path:
        """Pipedrive raw data lake partition path where data will be uploaded"""
        return Path(PIPEDRIVE_DATA_LAKE_PARTITION.base_path, self.run_suffix)

    @profile
    def run(self) -> None:
        """
        Runs all the steps of the etl
        """

        logger.info(f"Starting Pipedrive ETL with {self.run_suffix}...")
        extracted = self.extract()
        if not extracted:
            logger.warning("No Pipedrive data to be loaded. Finishing.")
            return

        file_paths = self.dump(extracted)
        transformed = self.transform(file_paths)
        loaded_dl, loaded_bq, loaded_pg = self.load(transformed)

        logger.info(
            f"Finished with {loaded_dl} file(s) loaded to data lake, "
            f"{loaded_bq} row(s) loaded to BQ, "
            f"{loaded_pg} row(s) loaded to PG!"
        )

    @log_pipeline_execution(step_name="pipedrive:extract")
    def extract(self) -> PipedriveExtracted:
        """
        Calls all Pipedrive API endpoints in parallel
        :returns: list of Pipedrive endpoints results
        """
        api_calls = {
            lambda: {"users": self._api_client.get_users()},
            lambda: {"organizations": self._api_client.get_organisations()},
            lambda: {"organization_fields": self._api_client.get_organisation_fields()},
            lambda: {"persons": self._api_client.get_persons()},
            lambda: {"person_fields": self._api_client.get_person_fields()},
            lambda: {"deals": self._api_client.get_deals()},
            lambda: {"deal_fields": self._api_client.get_deal_fields()},
        }

        results = dict()
        from concurrent.futures import ThreadPoolExecutor, as_completed

        with ThreadPoolExecutor(max_workers=len(api_calls)) as executor:
            futures = {executor.submit(api_call) for api_call in api_calls}
            for future in as_completed(futures):
                for k, v in future.result().items():
                    results[k] = v

        return PipedriveExtracted(**results)

    @log_pipeline_execution(step_name="pipedrive:dump")
    def dump(self, extracted: ExtractedType) -> list[Path]:
        """
        Dumps extracted data to parquet files
        :param extracted: extracted data
        :returns: list of paths to the files
        """

        logger.info(f"Dumping to '{self.local_path}' extracted data to parquet files...")

        self.local_path.mkdir(parents=True, exist_ok=True)

        file_paths = []
        for k, v in extracted.to_dict().items():
            file_path = f"{self.local_path}/{k}.parquet"
            df = DataFrame(v)
            if "_fields" in k:
                # to overcome pandas error:  Conversion failed for column 'options' with type object
                # https://stackoverflow.com/questions/72192736/i-cant-convert-df-to-parquet-by-data-type-error
                df["options"] = df["options"].astype(str)
                df["field_value"] = df["field_value"].astype(str)
            df.to_parquet(path=file_path, index=False)
            file_paths.append(Path(file_path))

        return file_paths

    @log_pipeline_execution(step_name="pipedrive:transform")
    def transform(self, file_paths: list[Path]) -> TransformedType:
        """
        Transforms extracted data
        :param file_paths: paths to the parquet files
        :returns: transformed data
        """
        logger.info("Transforming data...")
        transformer = self._transformer_creator(file_paths)
        transformed = transformer.transform()

        return transformed

    @log_pipeline_execution(step_name="pipedrive:load")
    def load(self, transformed: TransformedType) -> LoadedType:
        """
        Loads the raw data to the raw data lake, and transformed to BigQuery and Postgres tables
        :param transformed: transformed data
        :returns: number of loaded files and rows to BQ and PG
        """
        loaded_dl = self._load_raw_to_data_lake()
        loaded_bq, loaded_pg = self._load_transformed(transformed)

        return loaded_dl, loaded_bq, loaded_pg

    def _load_transformed(self, transformed: TransformedType) -> tuple[int, int]:
        """
        Applies the dataframe schema and loads the transformed to BigQuery and Postgres tables
        :param transformed: transformed data
        :returns: number of loaded rows to BQ and PG
        """

        pipedrive_df = DataFrameWithSchema(schema=PipedriveSchema.SCHEMA, dataframe=DataFrame(transformed))

        loaded_bq = self._load_biq_query(pipedrive_df)
        loaded_pg = self._load_postgres(pipedrive_df)

        return loaded_bq, loaded_pg

    def _load_raw_to_data_lake(self) -> int:
        """
        Uploads the raw data to the data lake
        :returns: number of loaded files
        """
        files_count = len(
            [path.relative_to(self._local_temp_path) for path in self.local_path.rglob("*.parquet") if path.is_file()]
        )
        logger.info(
            f"Loading {files_count} file(s) to '{self._raw_data_lake_repository.bucket_name}' "
            f"data lake '{self.data_lake_path}' location..."
        )
        self._raw_data_lake_repository.write_directory(
            local_directory=self._local_temp_path,
            partition=PIPEDRIVE_DATA_LAKE_PARTITION,
        )

        return files_count

    def _load_biq_query(self, data_frame: DataFrameWithSchema) -> int:
        """
        Full reloads BigQuery table.
        :param data_frame: transformed pandas dataframe
        """

        logger.info(f"Loading BigQuery '{self._bq_repository.get_table().full_name}' table...")
        results = self._bq_repository.reload_table(data_frame)
        logger.info(
            f"Loaded {results.total_rows} row(s) into BigQuery '{self._bq_repository.get_table().full_name}' table, "
            f"{format_bytes_pretty(results.total_bytes_billed)} billed."
        )

        return results.total_rows

    def _load_postgres(self, data_frame: DataFrameWithSchema) -> int:
        """
        Full reloads Postgres Analytics DB table.
        :param data_frame: transformed pandas dataframe
        :returns: number of inserted rows
        """
        logger.info(f"Loading PostgreSQL Analytics DB '{self._pg_repository.TABLE.full_name}' table...")
        return self._pg_repository.reload_table(data_frame)
