with custom_metrics as (
    select
        oef.org_id,
        off.key,
        off.field_type,
        oef.field_value::text as field_value,
        lower(off.name) as name,
        case
            when off.options = 'None' then null
            else try_cast(replace(off.options, chr(39), '"')::json as struct (id int, label text)[])
        end as options
    from
        organization_extra_fields as oef
    inner join
        organization_fields as off
        on oef.field_key = off.key
    where
        lower(
            off.name) in (
            'probability of default',
            'credit score when live',
            'credit score on monitor',
            'vat number',
            'onboarding reserve plan agreed',
            'finance verification date',
            'sepa status',
            'collection status',
            'supplier type'
        )
)

select
    oef.org_id,
    max(case
        when cm1.name = 'probability of default'
            then oef.field_value::numeric(5, 4)
    end) as probability_of_default,
    max(case
        when cm1.name = 'vat number'
            then oef.field_value
    end) as vat_number,
    max(case
        when cm2.name = 'credit score on monitor'
            then cm2.options.label::text
    end) as credit_score_on_monitor,
    max(case
        when cm2.name = 'credit score when live'
            then cm2.options.label::text
    end) as credit_score_when_live,
    max(case
        when cm2.name = 'onboarding reserve plan agreed'
            then cm2.options.label::text
    end) as onboarding_reserve_plan_agreed,
    max(case
        when cm1.name = 'finance verification date'
            then oef.field_value
    end) as finance_verification_date,
    max(case
        when cm2.name = 'sepa status'
            then cm2.options.label::text
    end) as sepa_status,
    max(case
        when cm2.name = 'collection status'
            then cm2.options.label::text
    end) as collection_status,
    max(case
        when cm1.name = 'supplier type' and cm1.field_type = 'set'
            then
                list_transform(
                    list_filter(
                        cm1.options,
                        option -> list_contains(
                            string_split(oef.field_value, ',')::int [],
                            option.id
                        )
                    ),
                    option -> option.label
                )
    end) as supplier_type
from
    organization_extra_fields as oef
left join
    custom_metrics as cm1
    on
        oef.org_id = cm1.org_id
        and oef.field_key = cm1.key
left join
    (
        select
            org_id,
            key,
            name,
            unnest(options) as options
        from
            custom_metrics
    ) as cm2
    on
        oef.org_id = cm2.org_id
        and oef.field_key = cm2.key
        and oef.field_value = cm2.options.id::text
group by
    oef.org_id
having
    credit_score_on_monitor is not null
    or credit_score_when_live is not null
    or probability_of_default is not null
    or vat_number is not null
    or onboarding_reserve_plan_agreed is not null
    or finance_verification_date is not null
    or sepa_status is not null
    or collection_status is not null
    or supplier_type is not null;
