# https://docs.sqlfluff.com/en/stable/configuration.html#new-project-configuration
[sqlfluff]
sql_file_exts = .sql,.dml,.ddl
dialect = duckdb
max_line_length = 120
exclude_rules = ambiguous.column_count, structure.column_order

[sqlfluff:indentation]
indented_joins = False
template_blocks_indent = True
indented_using_on = False
allow_implicit_indents = True

[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.identifiers]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.functions]
extended_capitalisation_policy = lower

[sqlfluff:rules:capitalisation.literals]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.types]
extended_capitalisation_policy = lower

[sqlfluff:rules:aliasing.table]
aliasing = explicit

[sqlfluff:rules:aliasing.column]
aliasing = explicit
