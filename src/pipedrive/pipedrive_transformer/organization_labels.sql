with labels as (
    select
        try_cast(
            replace(options, chr(39), '"')::j<PERSON> as struct(
                id int, label text
            ) []
        ) as labels
    from
        organization_fields
    where
        name = 'Labels'
        and field_type = 'set'
    limit 1
)

select
    o.id as org_id,
    list_transform(
        list_filter(
            labels,
            label -> list_contains(o.label_ids, label.id)
        ),
        label -> label.label
    ) as labels
from
    organizations as o
cross join labels
where
    o.label_ids is not null
    and len(o.label_ids) > 0;
