with extra_fields as (
    select
        id as org_id,
        unnest(        case
            when extra_fields is null then [{'field_key': null, 'field_value': null}]
            else extra_fields::struct(field_key text, field_value text)[]
        end, recursive:=true)
    from
        organizations
)

select
    *
from
    extra_fields
where
    org_id is not null
    and field_key is not null
    and field_value is not null;
