from pathlib import Path
from typing import Optional, Self

import duckdb
from dataclasses_json import DataClassJsonMixin
from pipedrive_model import (
    ClosedDate,
    DealExtraField,
    EnteredDate,
    LiveSelling,
    Manager,
    Merchant,
    OrganizationCustomField,
    OrganizationExtraField,
    OrganizationLabel,
    PersonExtraField,
    PersonRole,
    PipedriveTransformed,
)

from common.duckdb_repository import DuckDbRepository
from common.utils import format_file, to_snake_case

CURRENT_DIR = Path(__file__).resolve().parent


class PipedriveTransformer(DuckDbRepository):
    """Pipedrive pipedrive_transformer leveraging DuckDB in-memory database"""

    _extra_fields_exp = """
        case
            when extra_fields is null then [{'field_key': null, 'field_value': null}]
            else extra_fields::struct(field_key text, field_value text)[]
        end
    """

    @staticmethod
    def file_path(file_name: str) -> Path:
        """Returns file path for given transformation file name"""
        return Path(f"{CURRENT_DIR}/{file_name}.sql")

    def __init__(self, file_paths: list[Path]) -> None:
        super().__init__()
        self._file_paths = file_paths

    @classmethod
    def create(cls, file_paths: list[Path]) -> Self:
        return cls(file_paths)

    def build(self) -> list[PipedriveTransformed]:
        """
        Calculates the final results by joining all preceding tables

        :returns: list of dataclass PipedriveTransformed result
        """

        table_name = to_snake_case(PipedriveTransformed.__name__)
        query = format_file(file_path=self.file_path(table_name))
        self.query_to_table(query, table_name)

        return self.get_table_results(table_name=table_name, entity=PipedriveTransformed)

    def transform(self) -> list[PipedriveTransformed]:
        """
        Transforms the extracted data

        :returns: list of dataclass PipedriveTransformed result
        """
        transformed = (
            self.set_extracted()
            .set_organization_extra_fields()
            .set_organization_custom_fields()
            .set_organization_labels()
            .set_person_extra_fields()
            .set_deal_extra_fields()
            .set_managers()
            .set_merchants()
            .set_entered_dates()
            .set_closed_dates()
            .set_live_selling()
            .set_person_roles()
            .build()
        )

        return transformed

    def set_extracted(self) -> Self:
        """Loads extracted data into into tables"""

        for file_path in self._file_paths:
            table_name = file_path.stem
            self.drop_table(table_name)
            duckdb.read_parquet(str(file_path), connection=self._connection).to_table(table_name)

        return self

    def set_organization_extra_fields(self) -> Self:
        """Calculates OrganizationExtraField table"""

        table_name = self._get_table_name(OrganizationExtraField)
        query = format_file(file_path=self.file_path(table_name), extra_fields_exp=self._extra_fields_exp)

        self.query_to_table(query, table_name)

        return self

    def set_organization_custom_fields(self) -> Self:
        """Calculates Organization custom fields"""

        table_name = self._get_table_name(OrganizationCustomField)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_organization_labels(self) -> Self:
        """Calculates Organization labels"""

        table_name = self._get_table_name(OrganizationLabel)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_person_extra_fields(self) -> Self:
        """Calculates PersonExtraField table"""

        table_name = self._get_table_name(PersonExtraField)
        query = format_file(file_path=self.file_path(table_name), extra_fields_exp=self._extra_fields_exp)

        self.query_to_table(query, table_name)

        return self

    def set_deal_extra_fields(self) -> Self:
        """Calculates DealExtraField table"""

        table_name = self._get_table_name(DealExtraField)
        query = format_file(file_path=self.file_path(table_name), extra_fields_exp=self._extra_fields_exp)

        self.query_to_table(query, table_name)

        return self

    def set_managers(self) -> Self:
        """Calculates Manager table"""

        table_name = self._get_table_name(Manager)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_merchants(self) -> Self:
        """Calculates Merchant table"""

        table_name = self._get_table_name(Merchant)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_entered_dates(self) -> Self:
        """Calculates DateEntered table"""

        table_name = self._get_table_name(EnteredDate)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_closed_dates(self) -> Self:
        """Calculates DateClosed table"""

        table_name = self._get_table_name(ClosedDate)
        self.drop_table(table_name)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_live_selling(self) -> Self:
        """Calculates LiveSelling table"""

        table_name = self._get_table_name(LiveSelling)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def set_person_roles(self) -> Self:
        """Calculates PersonRole table"""

        table_name = self._get_table_name(PersonRole)
        query = format_file(file_path=self.file_path(table_name))

        self.query_to_table(query, table_name)

        return self

    def get_table_entities[T: DataClassJsonMixin](self, entity: type[T]) -> Optional[list[T]]:
        """
        Get table entities
        :param entity: entity type
        :returns: table rows mapped to list of entity type
        """

        table_name = self._get_table_name(entity)
        if self.table_exists(table_name):
            return self.get_table_results(table_name=table_name, entity=entity)

        return None

    @staticmethod
    def _get_table_name[T: DataClassJsonMixin](entity: type[T]) -> str:
        """Get table name"""

        return f"{to_snake_case(entity.__name__)}s"
