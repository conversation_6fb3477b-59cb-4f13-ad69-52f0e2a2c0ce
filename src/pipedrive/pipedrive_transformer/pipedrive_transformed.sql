select
    mg.new_sales_manager,
    nsmu.email as new_sales_manager_email,
    mr.merchant_id,
    mr.merchant_name,
    p.primary_email,
    p.email as all_emails,
    mg.account_manager,
    mu.email as account_manager_email,
    o.name as account_name,
    p.first_name,
    p.last_name,
    pr.contact_roles,
    pr.has_performance_role,
    ls.live_selling,
    de.date_entered,
    dc.date_closed,
    ocf.probability_of_default,
    ocf.vat_number,
    ocf.credit_score_on_monitor,
    ocf.credit_score_when_live,
    o.id as organization_id,
    ol.labels as organization_labels,
    ocf.onboarding_reserve_plan_agreed,
    ocf.sepa_status,
    ocf.collection_status,
    ocf.supplier_type,
    ocf.finance_verification_date
from
    organizations as o
inner join merchants as mr
on o.id = mr.org_id
left join organization_custom_fields as ocf
on o.id = ocf.org_id
left join organization_labels as ol
on o.id = ol.org_id
left join persons as p
on o.id = p.org_ref_id
left join managers as mg
on o.id = mg.org_id
left join users as mu
on lower(mg.account_manager) = lower(mu.name)
left join users as nsmu
on lower(mg.new_sales_manager) = lower(nsmu.name)
left join entered_dates as de
on
    o.id = de.org_id
    and (p.id = de.person_id or de.person_id is null)
left join closed_dates as dc
on o.id = dc.org_id
left join live_sellings as ls
on
    o.id = ls.org_id
    and (p.id = ls.person_id or ls.person_id is null)
left join person_roles as pr
on p.id = pr.person_id;
