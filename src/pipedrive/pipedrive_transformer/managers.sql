select
    oef.org_id,
    max(case when ofs.name = 'Account Manager' then ofs.options.label end) as account_manager,
    max(case when ofs.name = 'New Sales Manager' then ofs.options.label end) as new_sales_manager
from
    organization_extra_fields as oef
inner join
    (
        select
            key,
            name,
            unnest(try_cast(options as struct(id int, label text) [])) as options
        from
            organization_fields
    ) as ofs
on
    oef.field_key = ofs.key
    and ofs.name in ('Account Manager', 'New Sales Manager')
    and ofs.options.id::text = oef.field_value
group by
    oef.org_id
having
    account_manager is not null
    or new_sales_manager is not null;
