with extra_fields as (
    select
        id as deal_id,
        unnest(case
            when extra_fields is null then [{ 'field_key': null, 'field_value': null }]
            else extra_fields::struct(field_key text, field_value text) []
        end, recursive := true)
    from
        deals
)

select ef.*
from
    extra_fields as ef
where
    ef.deal_id is not null
    and ef.field_key is not null
    and ef.field_value is not null;
