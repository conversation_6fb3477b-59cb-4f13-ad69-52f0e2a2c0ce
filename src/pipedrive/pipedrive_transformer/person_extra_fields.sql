with extra_fields as (
    select
        id as person_id,
        org_ref_id as org_id,
        unnest(        case
            when extra_fields is null then [{'field_key': null, 'field_value': null}]
            else extra_fields::struct(field_key text, field_value text)[]
        end, recursive:=true)
    from
        persons
)

select
    org_id,
    person_id,
    field_key,
    field_value
from
    extra_fields
where
    org_id is not null
    and field_key is not null
    and field_value is not null;
