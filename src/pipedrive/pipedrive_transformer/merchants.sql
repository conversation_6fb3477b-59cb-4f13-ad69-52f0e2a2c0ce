select
    oef.org_id,
    max(
        case
            when off.name = 'Merchant ID' then try_cast(oef.field_value as int)
        end
    ) as merchant_id,
    max(case when off.name = 'refurbed display name' then oef.field_value end)
        as merchant_name
from
    organization_extra_fields as oef
inner join
    organization_fields as off
on
    oef.field_key = off.key
    and name in ('refurbed display name', 'Merchant ID')
where
    oef.field_value is not null
group by
    oef.org_id
having
    merchant_id is not null;
