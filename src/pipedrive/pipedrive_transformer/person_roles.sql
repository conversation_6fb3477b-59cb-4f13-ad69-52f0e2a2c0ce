with person_fields_cte as (
    select
        *,
        options::struct(color TEXT, id TEXT, label TEXT) [] as opts,
        list_transform(opts, o -> o.id::TEXT) as option_ids
    from
        person_fields
),

person_extra_fields_cte as (
    select
        *,
        regexp_split_to_array(field_value, '[, ]') as field_values
    from
        person_extra_fields
)

select
    pef.person_id,
    flatten(list_transform(
        pef.field_values,
        r -> list_filter(pf.opts, o -> o.id::TEXT == r)
    )) as options_filtered,
    list_transform(options_filtered, e -> e.label) as contact_roles,
    list_contains(contact_roles, 'Performance') as has_performance_role
from
    person_fields_cte as pf
inner join
    person_extra_fields_cte as pef
    on
        pf.key = pef.field_key
        and pf.name = 'Contact Role'
        and len(list_intersect(pef.field_values, pf.option_ids)) > 0;
