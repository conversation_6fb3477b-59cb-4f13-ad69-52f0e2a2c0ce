select
    d.org_ref_id as org_id,
    d.person_ref_id as person_id,
    min(def.field_value) as date_entered
from
    organizations as o
inner join deals as d
on o.id = d.org_ref_id
inner join deal_extra_fields as def
on d.id = def.deal_id
inner join deal_fields as df
on
    def.field_key = df.key
    and lower(df.name) = 'date entered'
group by
    d.org_ref_id,
    d.person_ref_id
having
    date_entered is not null;
