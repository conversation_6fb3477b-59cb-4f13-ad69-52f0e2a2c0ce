from pipedrive_model import PipedriveSchema, PipedriveTransformed

from common.bq_client import QueryResult
from common.bq_repository import BigQueryRepository
from common.pandas_utils import DataFrameWithSchema, to_decimal_type


class PipedriveBqRepository(BigQueryRepository):
    """
    Pipedrive BigQuery repository
    """

    DATASET: str = "analytics_transformed"
    TABLE: str = "pipedrive"

    def reload_table(self, transformed: DataFrameWithSchema, decimal_precision: int = 5) -> QueryResult:
        """
        Reloads pipedrive transformed table by transformed dataframe
        :param transformed: transformed data frame that matches `TABLE` schema
        :param decimal_precision: numeric precision for Decimal type
        :returns: job results with number of loaded rows and bytes
        """
        self.truncate_table()
        data_frame = to_decimal_type(
            transformed.dataframe_with_schema, PipedriveSchema.probability_of_default, decimal_precision
        )
        results = self.load_table(data_frame)

        return results

    def select_table(self) -> list[PipedriveTransformed]:
        """
        Select all rows from the pipedrive BigQuery table.
        Converts Decimal to float for probability_of_default column.
        :returns: list of `PipedriveTransformed`
        """
        rows = self.select().rows

        return [
            PipedriveTransformed(
                new_sales_manager=row.new_sales_manager,
                new_sales_manager_email=row.new_sales_manager_email,
                merchant_id=row.merchant_id,
                merchant_name=row.merchant_name,
                primary_email=row.primary_email,
                all_emails=row.all_emails,
                account_manager=row.account_manager,
                account_manager_email=row.account_manager_email,
                account_name=row.account_name,
                first_name=row.first_name,
                last_name=row.last_name,
                contact_roles=row.contact_roles,
                has_performance_role=row.has_performance_role,
                live_selling=row.live_selling,
                date_entered=row.date_entered,
                date_closed=row.date_closed,
                probability_of_default=float(row.probability_of_default) if row.probability_of_default else None,
                vat_number=row.vat_number,
                credit_score_on_monitor=row.credit_score_on_monitor,
                credit_score_when_live=row.credit_score_when_live,
                organization_id=row.organization_id,
                organization_labels=row.organization_labels,
                onboarding_reserve_plan_agreed=row.onboarding_reserve_plan_agreed,
                sepa_status=row.sepa_status,
                collection_status=row.collection_status,
                supplier_type=row.supplier_type,
                finance_verification_date=row.finance_verification_date,
            )
            for row in rows
        ]
