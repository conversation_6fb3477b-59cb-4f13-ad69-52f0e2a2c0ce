from pipedrive_model import PipedriveSchema, PipedriveTransformed
from sqlalchemy import <PERSON>ole<PERSON>, Date, DateTime, Integer, Numeric, String
from sqlalchemy.dialects.postgresql import ARRAY, JSONB

from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


class PipedrivePgRepository(SqlRepository):
    """
    Pipedrive PostgreSQL Analytics DB repo
    """

    ps = PipedriveSchema

    TABLE = SqlTable(schema_name="analytics", table_name="pipedrive")
    TABLE_DTYPES = {
        ps.new_sales_manager: String,
        ps.new_sales_manager_email: String,
        ps.merchant_id: Integer,
        ps.merchant_name: String,
        ps.primary_email: String,
        ps.all_emails: JSONB,
        ps.account_manager: String,
        ps.account_manager_email: String,
        ps.account_name: String,
        ps.first_name: String,
        ps.last_name: String,
        ps.contact_roles: ARRAY(String),
        ps.has_performance_role: <PERSON>olean,
        ps.live_selling: DateTime,
        ps.date_entered: Date,
        ps.date_closed: Date,
        ps.probability_of_default: Numeric,
        ps.vat_number: String,
        ps.credit_score_on_monitor: String,
        ps.credit_score_when_live: String,
        ps.organization_id: Integer,
        ps.organization_labels: ARRAY(String),
        ps.onboarding_reserve_plan_agreed: String,
        ps.sepa_status: String,
        ps.collection_status: String,
        ps.supplier_type: ARRAY(String),
        ps.finance_verification_date: Date,
    }

    def reload_table(self, transformed: DataFrameWithSchema) -> int:
        """
        Reloads pipedrive Analytics DB table by transformed dataframe
        Converts Python dictionary type do Postgres json
        :param transformed: transformed dataset that matches `TABLE` schema
        :returns: Number of rows affected
        """

        return self.insert(transformed.dataframe_with_schema, self.TABLE, truncate=True, dtype=self.TABLE_DTYPES)

    def select_table(self) -> list[PipedriveTransformed]:
        """
        Select all rows from the pipedrive table.
        Converts Decimal to float for probability_of_default column.
        :returns: list of `PipedriveTransformed`
        """
        rows = self.select(self.TABLE)

        return [
            PipedriveTransformed(
                new_sales_manager=row.new_sales_manager,
                new_sales_manager_email=row.new_sales_manager_email,
                merchant_id=row.merchant_id,
                merchant_name=row.merchant_name,
                primary_email=row.primary_email,
                all_emails=row.all_emails,
                account_manager=row.account_manager,
                account_manager_email=row.account_manager_email,
                account_name=row.account_name,
                first_name=row.first_name,
                last_name=row.last_name,
                contact_roles=row.contact_roles,
                has_performance_role=row.has_performance_role,
                live_selling=row.live_selling,
                date_entered=row.date_entered,
                date_closed=row.date_closed,
                probability_of_default=float(row.probability_of_default) if row.probability_of_default else None,
                vat_number=row.vat_number,
                credit_score_on_monitor=row.credit_score_on_monitor,
                credit_score_when_live=row.credit_score_when_live,
                organization_id=row.organization_id,
                organization_labels=row.organization_labels,
                onboarding_reserve_plan_agreed=row.onboarding_reserve_plan_agreed,
                sepa_status=row.sepa_status,
                collection_status=row.collection_status,
                supplier_type=row.supplier_type,
                finance_verification_date=row.finance_verification_date,
            )
            for row in rows
        ]
