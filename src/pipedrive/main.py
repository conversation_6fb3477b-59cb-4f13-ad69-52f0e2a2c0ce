import tempfile
from pathlib import Path

from etl import PipedriveEtl
from google.cloud.sql.connector import Connector
from pipedrive_api_client import PipedriveApiClient, PipedriveApiConfig
from pipedrive_bq_repo import PipedriveBqRepository
from pipedrive_model import PipedriveConfig
from pipedrive_pg_repo import PipedrivePgRepository
from pipedrive_transformer.pipedrive_transformer import PipedriveTransformer

from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig

config = PipedriveConfig()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)
api_token = get_secret(config.api_secret, config.project_id)
api_config = PipedriveApiConfig(api_token=api_token)
api_client = PipedriveApiClient(config=api_config)
raw_data_lake_repo = DataLakeRepository(config.raw_bucket)
bq_repo = PipedriveBqRepository()


def run() -> None:
    """
    Code entry point, called from analytics-workflow.
    """
    logger.info(
        f"Running {config.pipeline_name} run_id={config.run_id} with remove_local_files={config.remove_local_files} ..."
    )

    with Connector() as connector, tempfile.TemporaryDirectory(delete=config.remove_local_files) as local_path:
        pg_repo = PipedrivePgRepository(db_config=pg_config, connector=connector)
        etl = PipedriveEtl(
            config=config,
            api_client=api_client,
            transformer_creator=PipedriveTransformer.create,
            local_temp_path=Path(local_path),
            raw_data_lake_repository=raw_data_lake_repo,
            bq_repository=bq_repo,
            pg_repository=pg_repo,
        )
        etl.run()


if __name__ == "__main__":
    if config.is_local:
        pg_config = DatabaseConfig()
        # Set the pipeline run environment for local execution
        pipeline_run = PipelineRun()
        SupportsPipelineExecution.set_environment(pipeline_run)

    # Ensure that the required environment variables are set
    SupportsPipelineExecution.supports_pipeline_logging(config, raise_error=True)
    run()
