from pathlib import Path
from typing import Optional, Self

import duckdb
from dataclasses_json import DataClassJsonMixin
from pipedrive_model import (
    ClosedDate,
    DealExtraField,
    EnteredDate,
    LiveSelling,
    Manager,
    Merchant,
    OrganizationCustomField,
    OrganizationExtraField,
    OrganizationLabel,
    PersonExtraField,
    PersonRole,
    PipedriveTransformed,
)

from common.duckdb_repository import DuckDbRepository
from common.utils import to_snake_case


class PipedriveTransformer(DuckDbRepository):
    """Pipedrive transformer leveraging DuckDB in-memory database"""

    _extra_fields_exp = """
        case
            when extra_fields is null then [{'field_key': null, 'field_value': null}]
            else extra_fields::struct(field_key text, field_value text)[]
        end
    """

    def __init__(self, file_paths: list[Path]) -> None:
        super().__init__()
        self._file_paths = file_paths

    @classmethod
    def create(cls, file_paths: list[Path]) -> Self:
        return cls(file_paths)

    def build(self) -> list[PipedriveTransformed]:
        """
        Calculates the final results by joining all preceding tables

        :returns: list of dataclass PipedriveTransformed result
        """

        table_name = to_snake_case(PipedriveTransformed.__name__)
        query = """
                select
                    mg.new_sales_manager,
                    nsmu.email as new_sales_manager_email,
                    mr.merchant_id,
                    mr.merchant_name,
                    p.primary_email,
                    p.email as all_emails,
                    mg.account_manager,
                    mu.email as account_manager_email,
                    o.name as account_name,
                    p.first_name,
                    p.last_name,
                    pr.contact_roles,
                    pr.has_performance_role,
                    ls.live_selling,
                    de.date_entered,
                    dc.date_closed,
                    ocf.probability_of_default,
                    ocf.vat_number,
                    ocf.credit_score_on_monitor,
                    ocf.credit_score_when_live,
                    o.id as organization_id,
                    ol.labels as organization_labels,
                    ocf.onboarding_reserve_plan_agreed,
                    ocf.sepa_status,
                    ocf.collection_status,
                    ocf.supplier_type,
                    ocf.finance_verification_date
                from
                    organizations as o
                    inner join merchants as mr
                    on o.id = mr.org_id
                    left join organization_custom_fields as ocf
                    on o.id = ocf.org_id
                    left join organization_labels as ol
                    on o.id = ol.org_id
                    left join persons as p
                    on o.id = p.org_ref_id
                    left join managers as mg
                    on o.id = mg.org_id
                    left join users as mu
                    on lower(mg.account_manager) = lower(mu.name)
                    left join users as nsmu
                    on lower(mg.new_sales_manager) = lower(nsmu.name)
                    left join entered_dates as de
                    on o.id = de.org_id
                        and (p.id = de.person_id or de.person_id is null)
                    left join closed_dates as dc
                    on o.id = dc.org_id
                    left join live_sellings as ls
                    on o.id = ls.org_id
                        and (p.id = ls.person_id or ls.person_id is null)
                    left join person_roles as pr
                    on p.id = pr.person_id;
        """
        self.query_to_table(query, table_name)

        return self.get_table_results(table_name=table_name, entity=PipedriveTransformed)

    def transform(self) -> list[PipedriveTransformed]:
        """
        Transforms the extracted data

        :returns: list of dataclass PipedriveTransformed result
        """
        transformed = (
            self.set_extracted()
            .set_organization_extra_fields()
            .set_organization_custom_fields()
            .set_organization_labels()
            .set_person_extra_fields()
            .set_deal_extra_fields()
            .set_managers()
            .set_merchants()
            .set_entered_dates()
            .set_closed_dates()
            .set_live_selling()
            .set_person_roles()
            .build()
        )

        return transformed

    def set_extracted(self) -> Self:
        """Loads extracted data into into tables"""

        for file_path in self._file_paths:
            table_name = file_path.stem
            self.drop_table(table_name)
            duckdb.read_parquet(str(file_path), connection=self._connection).to_table(table_name)

        return self

    def set_organization_extra_fields(self) -> Self:
        """Calculates OrganizationExtraField table"""

        table_name = self._get_table_name(OrganizationExtraField)
        query = f"""
                with extra_fields as (
                                select
                                    id as org_id,
                                    unnest({self._extra_fields_exp}, recursive:=true)
                                from
                                    organizations)
                select *
                from
                    extra_fields
                where
                      org_id is not null
                  and field_key is not null
                  and field_value is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_organization_custom_fields(self) -> Self:
        """Calculates Organization custom fields"""

        table_name = self._get_table_name(OrganizationCustomField)
        query = """
                   with custom_metrics as (select
                                              oef.org_id,
                                              off.key,
                                              lower(off.name) as name,
                                              off.field_type,
                                              oef.field_value::text as field_value,
                                              case
                                                  when off.options = 'None' then null
                                                  else try_cast(replace(off.options, chr(39), '"')::json
                                                       as struct(id int, label text)[])
                                              end as options
                                          from
                                              organization_extra_fields oef
                                          inner join
                                              organization_fields as off
                                          on  oef.field_key = off.key
                                          where
                                              lower(off.name) in ('probability of default',
                                                                  'credit score when live',
                                                                  'credit score on monitor',
                                                                  'vat number',
                                                                  'onboarding reserve plan agreed',
                                                                  'finance verification date',
                                                                  'sepa status',
                                                                  'collection status',
                                                                  'supplier type')
                                          )

                    select
                        oef.org_id,
                        max(case
                                when cm1.name = 'probability of default'
                                    then oef.field_value:: numeric (5, 4) end) as probability_of_default,
                        max(case
                                when cm1.name = 'vat number'
                                    then oef.field_value end) as vat_number,
                        max(case
                                when cm2.name = 'credit score on monitor'
                                    then cm2.options.label::text end) as credit_score_on_monitor,
                        max(case
                                when cm2.name = 'credit score when live'
                                    then cm2.options.label::text end) as credit_score_when_live,
                        max(case
                                when cm2.name = 'onboarding reserve plan agreed'
                                    then cm2.options.label::text end) as onboarding_reserve_plan_agreed,
                        max(case
                                when cm1.name = 'finance verification date'
                                    then oef.field_value end) as finance_verification_date,
                        max(case
                                when cm2.name = 'sepa status'
                                    then cm2.options.label::text end) as sepa_status,
                        max(case
                                when cm2.name = 'collection status'
                                    then cm2.options.label::text end) as collection_status,
                        max(case
                                when cm1.name = 'supplier type' and cm1.field_type = 'set'
                                    then
                                        list_transform(
                                                list_filter(
                                                        cm1.options,
                                                        option -> list_contains(
                                                                    string_split(oef.field_value, ',')::int[], option.id
                                                                  )
                                                ),
                                            option -> option.label) end) as supplier_type
                    from
                        organization_extra_fields oef
                        left join
                            custom_metrics cm1
                            on oef.org_id = cm1.org_id
                                and oef.field_key = cm1.key
                        left join
                            (select
                                 org_id, key, name, unnest(options) as options
                             from
                                 custom_metrics) as cm2
                             on oef.org_id = cm2.org_id
                                and oef.field_key = cm2.key
                                and oef.field_value = cm2.options.id::text
                    group by
                        oef.org_id
                    having
                        credit_score_on_monitor is not null
                      or credit_score_when_live is not null
                      or probability_of_default is not null
                      or vat_number is not null
                      or onboarding_reserve_plan_agreed is not null
                      or finance_verification_date is not null
                      or sepa_status is not null
                      or collection_status is not null
                      or supplier_type is not null;
                """

        self.query_to_table(query, table_name)

        return self

    def set_organization_labels(self) -> Self:
        """Calculates Organization labels"""

        table_name = self._get_table_name(OrganizationLabel)
        query = """
                    select
                        o.id as org_id,
                        list_transform(
                            list_filter(
                                labels,
                                label -> list_contains(o.label_ids, label.id)
                            ),
                            label -> label.label
                        ) as labels
                    from
                        organizations o
                    cross join (
                        select
                            try_cast(replace(options, chr(39), '"')::json as struct(id int, label text)[]) as labels
                        from
                            organization_fields
                        where
                            name = 'Labels'
                        and field_type = 'set'
                        limit 1) as labels
                    where
                        o.label_ids is not null
                    and len(o.label_ids) > 0;
                """

        self.query_to_table(query, table_name)

        return self

    def set_person_extra_fields(self) -> Self:
        """Calculates PersonExtraField table"""

        table_name = self._get_table_name(PersonExtraField)
        query = f"""
                with extra_fields as (
                                select
                                    id as person_id,
                                    org_ref_id as org_id,
                                    unnest({self._extra_fields_exp}, recursive:=true)
                                from
                                    persons)
                select *
                from
                    extra_fields
                where
                      org_id is not null
                  and field_key is not null
                  and field_value is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_deal_extra_fields(self) -> Self:
        """Calculates DealExtraField table"""

        table_name = self._get_table_name(DealExtraField)
        query = f"""
                with extra_fields as (
                                select
                                    id as deal_id,
                                    unnest({self._extra_fields_exp}, recursive:=true)
                                from
                                    deals)
                select *
                from
                    extra_fields
                where
                      deal_id is not null
                  and field_key is not null
                  and field_value is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_managers(self) -> Self:
        """Calculates Manager table"""

        table_name = self._get_table_name(Manager)
        query = """
                select
                    oef.org_id,
                    max(case when off.name = 'Account Manager' then off.options.label end) as account_manager,
                    max(case when off.name = 'New Sales Manager' then off.options.label end) as new_sales_manager,
                from
                    organization_extra_fields oef
                    inner join
                        (select
                             key, name, unnest(try_cast(options as struct(id int, label text)[])) as options
                         from
                             organization_fields) as off
                        on oef.field_key = off.key
                            and off.name in ('Account Manager', 'New Sales Manager')
                            and off.options.id::text = oef.field_value
                group by
                    oef.org_id
                having
                    account_manager is not null
                    or new_sales_manager is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_merchants(self) -> Self:
        """Calculates Merchant table"""

        table_name = self._get_table_name(Merchant)
        query = """
                select
                    oef.org_id,
                    max(case when off.name = 'Merchant ID' then try_cast(oef.field_value as int) end) as merchant_id,
                    max(case when off.name = 'refurbed display name' then oef.field_value end) as merchant_name
                from
                    organization_extra_fields oef
                    inner join
                        organization_fields as off
                        on oef.field_key = off.key
                            and name in ('refurbed display name', 'Merchant ID')
                where
                    oef.field_value is not null
                group by
                    oef.org_id
                having
                    merchant_id is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_entered_dates(self) -> Self:
        """Calculates DateEntered table"""

        table_name = self._get_table_name(EnteredDate)
        query = """
                select
                    d.org_ref_id as org_id,
                    d.person_ref_id as person_id,
                    min(def.field_value) as date_entered
                from
                    organizations as o
                    inner join deals d
                    on o.id = d.org_ref_id
                    inner join deal_extra_fields def
                    on d.id = def.deal_id
                    inner join deal_fields df
                    on def.field_key = df.key
                        and lower(df.name) = 'date entered'
                group by
                    d.org_ref_id,
                    d.person_ref_id
                having
                    date_entered is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_closed_dates(self) -> Self:
        """Calculates DateClosed table"""

        table_name = self._get_table_name(ClosedDate)
        self.drop_table(table_name)
        query = """
                select
                    oef.org_id,
                    max(oef.field_value) as date_closed
                from
                    organization_extra_fields oef
                    inner join
                        organization_fields as off
                        on oef.field_key = off.key
                            and lower(off.name) = 'seller guide signed'
                group by
                    oef.org_id
                having
                    date_closed is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_live_selling(self) -> Self:
        """Calculates LiveSelling table"""

        table_name = self._get_table_name(LiveSelling)
        query = """
                select
                    d.org_ref_id as org_id,
                    d.person_ref_id as person_id,
                    min(d.won_time) as live_selling
                from
                    deals d
                where
                      d.org_ref_id is not null
                  and d.won_time is not null
                group by
                    d.org_ref_id,
                    d.person_ref_id
                having
                    live_selling is not null;
        """

        self.query_to_table(query, table_name)

        return self

    def set_person_roles(self) -> Self:
        """Calculates PersonRole table"""

        table_name = self._get_table_name(PersonRole)
        query = """
                with person_fields_cte as (
                    select
                       *,
                       options::struct(color text, id text, label text)[] as opts,
                       list_transform(opts, o -> o.id::text) as option_ids
                    from
                       person_fields
                ),
                person_extra_fields_cte as (
                    select
                        *,
                        regexp_split_to_array(field_value, '[, ]') as field_values
                    from
                        person_extra_fields
                )

                select
                    pef.person_id,
                    flatten(list_transform(pef.field_values,
                                           r -> list_filter(pf.opts, o -> o.id::text == r))) as options_filtered,
                    list_transform(options_filtered, e -> e.label) as contact_roles,
                    list_contains(contact_roles, 'Performance') as has_performance_role
                from
                    person_fields_cte pf
                    inner join
                        person_extra_fields_cte as pef
                        on pf.key = pef.field_key
                            and pf.name = 'Contact Role'
                            and len(list_intersect(pef.field_values, pf.option_ids)) > 0;
        """

        self.query_to_table(query, table_name)

        return self

    def get_table_entities[T: DataClassJsonMixin](self, entity: type[T]) -> Optional[list[T]]:
        """
        Get table entities
        :param entity: entity type
        :returns: table rows mapped to list of entity type
        """

        table_name = self._get_table_name(entity)
        if self.table_exists(table_name):
            return self.get_table_results(table_name=table_name, entity=entity)

        return None

    @staticmethod
    def _get_table_name[T: DataClassJsonMixin](entity: type[T]) -> str:
        """Get table name"""

        return f"{to_snake_case(entity.__name__)}s"
