import dataclasses
from http import HTTPStatus
from typing import Optional

from googleapiclient.errors import HttpError

from common.cloud_sql_instance_client.client_base import (
    CloudSQLBaseClient,
    CloudSQLClientError,
    CloudSQLConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    CloudSQLCreateInstanceParams,
    CloudSQLInstance,
    CloudSQLRestoreBackupParams,
    InstanceSettings,
)
from common.logger import get_logger
from common.timing import timing

logger = get_logger()


class CloudSQLInstanceClient(CloudSQLBaseClient):
    """
    A custom Cloud SQL Instance client to interact with the service.
    """

    def __init__(self, config: CloudSQLConfig):
        super().__init__(config)

    def describe_instance(self, name: str) -> Optional[CloudSQLInstance]:
        """
        Retrieves a resource containing information about a Cloud SQL instance
        :param name: Name of the Cloud SQL instance. This does not include the project ID
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#get # noqa
        :returns: an object describing the instance or None if instance does not exist
        """
        logger.info(f"Describing '{name}' Cloud SQL instance...")

        request = self._service.instances().get(project=self._config.project_id, instance=name)
        try:
            response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.info(f"The Cloud SQL instance '{name}' does not exist.")
                return None
            else:
                raise

        return CloudSQLInstance.from_dict(response)

    @timing(logger)
    def create_instance(
        self, params: CloudSQLCreateInstanceParams, wait_for_completion: bool = True
    ) -> CloudSQLInstance:
        """
        Creates empty Cloud SQL instance
        :param params: params describing new instance
        :param wait_for_completion: whether to run synchronous
        Ref.
            https://cloud.google.com/sql/docs/postgres/create-instance#rest-v1beta4
            https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1beta4/operations
        :returns: an object describing the instance
        """
        logger.info(f"Creating Cloud SQL instance with following settings: {params.to_dict()}")
        request = self._service.instances().insert(project=self._config.project_id, body=params.to_dict())
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        return self.describe_instance(name=params.name)

    @timing(logger)
    def drop_instance(self, name: str, wait_for_completion: bool = True) -> None:
        """
        Drops Cloud SQL instance
        :param name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#delete # noqa
        """

        logger.info(f"Dropping Cloud SQL '{name}' instance...")
        request = self._service.instances().delete(project=self._config.project_id, instance=name)
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

    def start_instance(self, instance_name: str, wait_for_completion: bool = True) -> CloudSQLInstance:
        """
        Starts Cloud SQL instance when stopped
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :returns: an object describing the instance
        """

        return self._start_stop_instance(instance_name, wait_for_completion, True)

    def stop_instance(self, instance_name: str, wait_for_completion: bool = True) -> CloudSQLInstance:
        """
        Stops Cloud SQL instance when started
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :returns: an object describing the instance
        """

        return self._start_stop_instance(instance_name, wait_for_completion, False)

    def get_instances(self) -> list[CloudSQLInstance]:
        """
        Lists instances under a given project in the alphabetical order of the instance name
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#list # noqa
        :returns: an object describing the instance
        """

        request = self._service.instances().list(project=self._config.project_id)
        response = request.execute()

        instances = response.get("items", [])
        if not instances:
            logger.warning(f"No Cloud SQL instance(s) found in the '{self._config.project_id}' project!")

        return [CloudSQLInstance.from_dict(instance) for instance in instances]

    @timing(logger)
    def restore_backup(
        self, instance_name: str, backup_params: CloudSQLRestoreBackupParams, wait_for_completion: bool = True
    ) -> None:
        """
        Restores Cloud SQL backup into given instance
        :param instance_name: Name of the Cloud SQL instance (excluding project ID) to which backup will be restored.
        :param backup_params: backup context, see CloudSQLBackupContext
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#restoreBackup # noqa
        """

        logger.info(
            f"Restoring '{backup_params.restore_backup_context.backup_run_id}' Cloud SQL backup "
            f"into '{instance_name}' instance..."
        )
        request = self._service.instances().restoreBackup(
            project=self._config.project_id, instance=instance_name, body=backup_params.to_dict()
        )
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

    def _start_stop_instance(
        self, instance_name: str, wait_for_completion: bool = True, start: bool = True
    ) -> CloudSQLInstance:
        """
        Starts/Stops Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :param start: whether to start the instance
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#patch # noqa
        :returns: an object describing the instance
        :raises CloudSQLInstanceClientError: when SQL instance does not exist
        """
        instance = self.describe_instance(name=instance_name)

        if not instance:
            raise CloudSQLClientError(f"The Cloud SQL instance '{instance_name}' does not exist.")

        expected_policy = "NEVER" if start else "ALWAYS"
        update_policy = "ALWAYS" if start else "NEVER"

        if instance.settings.activation_policy == expected_policy:
            settings = dataclasses.replace(instance, settings=InstanceSettings(activation_policy=update_policy))
        else:
            logger.warning(f"Cloud SQL '{instance_name}' instance is not {"stopped" if start else "started"}...")
            return instance

        logger.info(f"{"Starting" if start else "Stopping"} Cloud SQL '{instance_name}' instance...")
        request = self._service.instances().patch(
            project=self._config.project_id, instance=instance_name, body=settings.to_dict()
        )
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        return self.describe_instance(name=instance_name)
