import dataclasses
import time
from abc import ABC
from functools import cached_property
from typing import Any

from googleapiclient import discovery
from tenacity import retry

from common.config import Config
from common.logger import get_logger
from common.retry_config import RETRY_CONFIG

logger = get_logger()


@dataclasses.dataclass(frozen=True)
class CloudSQLConfig(Config):
    """
    - sleep_in_seconds: number of seconds to sleep while waiting for SQL operation to complete
    """

    sleep_in_seconds: int = 10


class CloudSQLClientError(ValueError):
    """Cloud SQL Client error"""


class CloudSQLBaseClient(ABC):
    """
    Cloud SQL backup and instance base client.
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/index.html
    """

    def __init__(self, config: CloudSQLConfig):
        self._config = config

        # Construct the service object for the interacting with the Cloud SQL Admin API.
        self._service = discovery.build("sqladmin", "v1beta4")

    @cached_property
    def config(self) -> Config:
        """Provides read-only config property."""

        return self._config

    def _wait_for_completion(self, response: dict[str, Any], wait_for_completion: bool) -> None:
        """
        Wait for completion if SQL operation found
        :param response: dictionary containing SQL operation name
        :param wait_for_completion: whether to run synchronous
        :raises CloudSQLInstanceClientError: when SQL operation not found
        """
        if wait_for_completion:
            operation_name = response.get("name")
            if not operation_name:
                raise CloudSQLClientError("No SQL operation name returned!")

            self._wait_for_operation_to_complete(operation_name)

    @retry(reraise=True, wait=RETRY_CONFIG)
    def _wait_for_operation_to_complete(self, operation: str) -> None:
        """
        Wait for SQL operation to complete
        :param operation: SQL operation name
        :raises CloudSQLInstanceClientError: when no SQL operation status found
        Ref.
            https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1beta4/operations
            https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.operations.html # noqa
        """
        completed = False

        while not completed:
            status = self._get_operation(name=operation).get("status")
            if not status:
                raise CloudSQLClientError("No operation status returned!")

            if status.lower() not in ("pending", "running"):
                completed = True

            if completed:
                logger.info(f"{operation=} completed with status '{status}'...")
            else:
                logger.info(f"Waiting for {operation=} to complete, current status is '{status}'...")
                time.sleep(self._config.sleep_in_seconds)

    def _get_operation(self, name: str) -> dict[str, Any]:
        """
        Get SQL operation for given name
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.operations.html#get # noqa
        """

        request = self._service.operations().get(project=self._config.project_id, operation=name)
        response = request.execute()

        return response
