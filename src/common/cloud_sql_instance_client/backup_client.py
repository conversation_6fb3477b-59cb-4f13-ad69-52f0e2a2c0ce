from http import H<PERSON><PERSON>tatus
from typing import Optional

from googleapiclient.errors import HttpError

from common.cloud_sql_instance_client.client_base import (
    CloudSQLBaseClient,
    CloudSQLClientError,
    CloudSQLConfig,
)
from common.cloud_sql_instance_client.instance_model import Backup, BackupType
from common.logger import get_logger
from common.timing import timing

logger = get_logger()


class CloudSQLBackupClient(CloudSQLBaseClient):
    """
    A custom Cloud SQL backup client to interact with the service.
    """

    def __init__(self, config: CloudSQLConfig):
        super().__init__(config)

    def get_backups(self, instance_name: str, max_results: int = 10) -> list[Backup]:
        """
        Get Cloud SQL backups in the reverse chronological order of the backup initiation time
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param max_results: Maximum number of backup runs per response
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#list # noqa
        :returns: list of objects describing the backup
        """
        logger.info(f"Getting Cloud SQL '{instance_name}' instance backups...")
        request = self._service.backupRuns().list(
            project=self._config.project_id, instance=instance_name, maxResults=max_results
        )
        response = request.execute()

        backups = response.get("items", [])
        if not backups:
            logger.warning(f"No backups found for the '{instance_name}' Cloud SQL instance!")

        return [Backup.from_dict(backup) for backup in backups]

    def get_latest_backup(self, instance_name: str) -> Optional[Backup]:
        """
        Get Cloud SQL latest successful backup if present
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :returns: objects describing the backup or None
        """
        latest_backup = None

        successful_backups = (backup for backup in self.get_backups(instance_name) if backup.status == "SUCCESSFUL")
        if not successful_backups:
            logger.warning(f"No successful backups found for the '{instance_name}' Cloud SQL instance!")
        else:
            latest_backup = sorted(successful_backups, key=lambda x: x.end_time, reverse=True)[0]

        return latest_backup

    @timing(logger)
    def create_backup(
        self, instance_name: str, description: Optional[str] = None, wait_for_completion: bool = True
    ) -> Backup:
        """
        Creates on-demand backup of a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param description: The description of the backup
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#insert # noqa
        :returns: an object describing the backup
        :raises CloudSQLInstanceClientError: when cannot create the backup
        """
        logger.info(f"Creating on-demand backup for Cloud SQL instance '{instance_name}'...")

        body = {"description": description} if description else {}
        request = self._service.backupRuns().insert(project=self._config.project_id, instance=instance_name, body=body)

        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        backup_id = response.get("backupContext", {}).get("backupId")
        if not backup_id:
            raise CloudSQLClientError("Backup ID not found in the response!")

        backup = self.describe_backup(instance_name, backup_id)
        if not backup:
            raise CloudSQLClientError(f"Cannot describe already created '{backup_id}' backup ID!")

        return backup

    @timing(logger)
    def delete_backup(self, instance_name: str, backup_id: str, wait_for_completion: bool = True) -> None:
        """
        Deletes a backup of a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param backup_id: The ID of the backup to delete
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#delete # noqa
        """
        logger.info(f"Deleting backup '{backup_id}' for Cloud SQL instance '{instance_name}'...")

        try:
            request = self._service.backupRuns().delete(
                project=self._config.project_id, instance=instance_name, id=backup_id
            )
            response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.warning(f"Backup with ID '{backup_id}' not found!")
                return
            else:
                raise

        self._wait_for_completion(response, wait_for_completion)

    @timing(logger)
    def delete_backups(self, instance_name: str, description: str, wait_for_completion: bool = True) -> int:
        """
        Deletes ON_DEMAND backups with the given description for a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param description: The description of the backups to delete, cannot be empy string
        :param wait_for_completion: whether to run synchronous
        :returns: The number of backups deleted
        :raises CloudSQLInstanceClientError: when a description is an empty string
        """
        logger.info(
            f"Deleting all '{BackupType.ON_DEMAND}' backups "
            f"with description '{description}' for Cloud SQL instance '{instance_name}'..."
        )

        if not description:
            raise CloudSQLClientError("Cannot specify empty description!")

        backups = self.get_backups(instance_name)
        matching_backups = [
            backup for backup in backups if backup.type == BackupType.ON_DEMAND and backup.description == description
        ]

        if not matching_backups:
            logger.warning(
                f"No '{BackupType.ON_DEMAND}' backups "
                f"with description '{description}' found for instance '{instance_name}'"
            )
            return 0

        logger.info(f"Found {len(matching_backups)} matching backup(s) to delete...")

        for backup in matching_backups:
            self.delete_backup(instance_name, backup.id, wait_for_completion)

        return len(matching_backups)

    def describe_backup(self, instance_name: str, backup_id: str) -> Optional[Backup]:
        """
        Retrieves a resource containing information about a Cloud SQL backup
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param backup_id: The ID of the backup to retrieve
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#get # noqa
        :returns: an object describing the backup, None if object does not exist
        """
        try:
            request = self._service.backupRuns().get(
                project=self._config.project_id, instance=instance_name, id=backup_id
            )
            backup_response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.warning(f"Backup with ID '{backup_id}' not found!")
                return None
            else:
                raise

        return Backup.from_dict(backup_response)
