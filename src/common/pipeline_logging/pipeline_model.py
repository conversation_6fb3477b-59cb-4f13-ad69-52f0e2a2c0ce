import dataclasses
import os
from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable

from dataclasses_json import DataClassJsonMixin

from common.logger import get_logger
from common.sql_repository import SqlRepository
from common.utils import to_space_separated

logger = get_logger()


class PipelineExecutionError(ValueError):
    """Pipeline execution error"""


@runtime_checkable
class SupportsPipelineRun(Protocol):
    """Interface for classes that support pipeline run metadata."""

    @property
    def pipeline_name(self) -> str:
        """Name of the pipeline."""
        ...

    @property
    def run_id(self) -> str:
        """Unique ID of the run."""
        ...


@dataclasses.dataclass(frozen=True)
class PipelineRun(DataClassJsonMixin):
    """
    Distributed pipeline run metadata.
    Default values are used for local development and testing outside of GCP Workflows.
    It should be in line with `src/sql-pg/schema/functions/R__0011_log_pipeline_execution.sql`
    """

    pipeline_name: str = "test-pipeline"
    run_id: str = "test-run"
    log_id: int = 1


class SupportsPipelineExecution(ABC):
    """
    Interface for classes that support pipeline execution logging.
    Classes should have a `run_id` property and a `SqlRepository` instance.
    """

    PIPELINE_NAME: str = "PIPELINE_NAME"
    RUN_ID: str = "RUN_ID"

    @property
    def pipeline_name(self) -> str:
        """
        Name of the pipeline:
        - use `PIPELINE_NAME` environment variable if set,
        - in other cases, ue the name provided by the PipelineRun class.
        """
        if distributed_pipeline := os.environ.get(self.PIPELINE_NAME):
            pipeline_name = distributed_pipeline
        else:
            pipeline_name = PipelineRun.pipeline_name
            logger.warning(f"No distributed pipeline run! Falling back to {pipeline_name=}.")

        return pipeline_name

    @property
    def run_id(self) -> str:
        """
        Unique ID of the run
        In distributed pipline, the run_id is set by GCP Workflow and passed via `RUN_ID` env variable.
        In other cases, it is provided by the PipelineRun class.
        """
        if distributed_run := os.environ.get(self.RUN_ID):
            run_id = distributed_run
        else:
            run_id = PipelineRun.run_id
            logger.warning(f"No distributed run_id for the pipeline! Falling back to {run_id=}.")

        return run_id

    @property
    @abstractmethod
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository instance used for logging pipeline execution."""

    def get_step_name(self, function_name: str) -> str:
        """
        Return step name for the given function.
        It must be unique within the whole pipeline run, across all components.

        :param function_name: Name of the function to get step name for
        :returns: Step name derived from the function name
        """
        if not function_name:
            return self.pipeline_name

        return to_space_separated(function_name)

    @classmethod
    def supports_pipeline_logging(cls, obj: SupportsPipelineRun | PipelineRun, raise_error: bool = False) -> bool:
        """
        Checks if given instance supports logging with PipelineExecutionLogger.
        :param obj: Instance to check
        :param raise_error: If True, raises PipelineExecutionError if the instance does not support logging
        :returns: True if instance supports pipeline logging, False otherwise
        """
        result = (
            isinstance(obj.pipeline_name, str)
            and bool(obj.pipeline_name.strip())
            and isinstance(obj.run_id, str)
            and bool(obj.run_id.strip())
        )
        if raise_error and not result:
            raise PipelineExecutionError(f"Object does not support logging, {obj=}!")

        return result

    @classmethod
    def set_environment(cls, obj: SupportsPipelineRun | PipelineRun) -> None:
        """
        Sets the environment variables for the pipeline run.

        Mandatory for distributed pipelines where:
         - `pipeline_name` and `run_id` are not implemented
         - or environment variables are not set.

        :param obj: Instance to set environment variables from.
        """
        cls.supports_pipeline_logging(obj, raise_error=True)
        os.environ[cls.PIPELINE_NAME] = obj.pipeline_name
        os.environ[cls.RUN_ID] = obj.run_id
