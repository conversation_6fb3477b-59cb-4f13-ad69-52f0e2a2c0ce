import dataclasses
import os
from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable

from dataclasses_json import DataClassJsonMixin

from common.sql_repository import SqlRepository
from common.utils import to_space_separated


class PipelineExecutionError(ValueError):
    """Pipeline execution error"""


@runtime_checkable
class SupportsPipelineRun(Protocol):
    """Interface for classes that support pipeline run metadata."""

    @property
    def pipeline_name(self) -> str:
        """Name of the pipeline."""
        ...

    @property
    def run_id(self) -> str:
        """Unique ID of the run."""
        ...


@dataclasses.dataclass(frozen=True)
class PipelineRun(DataClassJsonMixin):
    """
    Distributed pipeline run metadata.
    Default values are used for local development and testing purposes.
    It should be in line with `src/sql-pg/schema/functions/R__0011_log_pipeline_execution.sql`
    """

    pipeline_name: str = "test-pipeline"
    run_id: str = "test-run"
    log_id: int = 1


class SupportsPipelineExecution(ABC):
    """
    Interface for classes that support pipeline execution logging.
    Classes should have a `run_id` property and a `SqlRepository` instance.
    """

    PIPELINE_NAME: str = "PIPELINE_NAME"
    RUN_ID: str = "RUN_ID"

    @property
    def pipeline_name(self) -> str:
        """
        Name of the pipeline:
        - use `PIPELINE_NAME` environment variable if set,
        - otherwise use the class name as a comma-separated string.
        """
        if distributed_pipeline := os.environ.get(self.PIPELINE_NAME):
            return distributed_pipeline

        return to_space_separated(self.__class__.__name__)

    @property
    def run_id(self) -> str:
        """Unique ID of the run"""
        if distributed_run := os.environ.get(self.RUN_ID):
            return distributed_run

        raise PipelineExecutionError("Undefined run_id for the pipeline!")

    @property
    @abstractmethod
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository instance used for logging pipeline execution."""

    def get_step_name(self, function_name: str) -> str:
        """
        Return step name for the given function.
        It must be unique within the whole pipeline run, across all components.

        :param function_name: Name of the function to get step name for
        :returns: Step name derived from the function name
        """
        if not function_name:
            return self.pipeline_name

        return to_space_separated(function_name)

    @classmethod
    def supports_pipeline_logging(cls, obj: SupportsPipelineRun | PipelineRun, raise_error: bool = False) -> bool:
        """
        Checks if given instance supports logging with PipelineExecutionLogger.
        :param obj: Instance to check
        :param raise_error: If True, raises PipelineExecutionError if the instance does not support logging
        :returns: True if instance supports pipeline logging, False otherwise
        """
        result = (
            isinstance(obj.pipeline_name, str)
            and bool(obj.pipeline_name.strip())
            and isinstance(obj.run_id, str)
            and bool(obj.run_id.strip())
        )
        if raise_error and not result:
            raise PipelineExecutionError(f"Object does not support logging, {obj=}!")

        return result

    @classmethod
    def set_environment(cls, obj: SupportsPipelineRun | PipelineRun) -> None:
        """
        Sets the environment variables for the pipeline run.

        Mandatory for distributed pipelines where:
         - `pipeline_name` and `run_id` are not implemented
         - or environment variables are not set.

        :param obj: Instance to set environment variables from.
        """
        cls.supports_pipeline_logging(obj, raise_error=True)
        os.environ[cls.PIPELINE_NAME] = obj.pipeline_name
        os.environ[cls.RUN_ID] = obj.run_id
