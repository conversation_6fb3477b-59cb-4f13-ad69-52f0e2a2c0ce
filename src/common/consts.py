# 202412181259 Rebuild all pipelines if that timestamp is updated
from enum import StrEnum

# Project scope
DEFAULT_ENV = "local"
PROJECT_NAME = "analytics-pipelines"
GCP_REGION = "europe-west3"
GCP_MULTI_REGION = "EU"
GCP_CONSOLE_BASE_URL = "https://console.cloud.google.com"

PROD_ANALYTICS_PROJECT_NAME = "refb-analytics"
PROD_ANALYTICS_SQL_INSTANCE = "analytics-08ce"

STAGING_GOOGLE_PROJECT_ID = f"{PROD_ANALYTICS_PROJECT_NAME}-staging"
STAGING_ANALYTICS_SQL_INSTANCE = "analytics-0ad7e796"

# Secret manager secrets
ANALYTICS_CLOUD_SQL_SECRET_ID = "analytics-db-cf-writer"
ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID = "analytics-db-cf-reader"

# Range for Postgres serial data type: https://www.postgresql.org/docs/current/datatype-numeric.html
MAX_SERIAL = 2147483647


class AlertCriticality(StrEnum):
    """
    Alert criticality used by cloud functions and jobs.
    Case-sensitive, do not change casing!

    Note that `StrEnum` members can be compared with `str` without casting, like so:
    -------------------------------
    >>> AlertCriticality.P1 == 'p1'
    """

    P1 = "p1"
    P2 = "p2"


class AlertSeverity(StrEnum):
    """
    Alert severity used by the alerting module.
    Case-sensitive, do not change casing!
    """

    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
