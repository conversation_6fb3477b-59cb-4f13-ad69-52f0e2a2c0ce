from cs_model import CATEGORY_SLUGS_SCHEMA, COUNTRIES_SCHEMA

from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlProcedure, SqlTable
from common.sql_repository import SqlRepository


class CategorySlugsPGRepository(SqlRepository):
    """
    Abstraction over category_levels and category_slugs PG tables.
    """

    CATEGORY_SLUGS_TABLE = SqlTable(schema_name="export_etl", table_name="category_slugs")
    COUNTRIES_VIEW = SqlTable(schema_name="platform_export", table_name="countries")

    CREATE_CATEGORY_SLUGS = SqlProcedure(schema_name="export_etl", procedure_name="create_category_slugs")
    SELECT_CATEGORY_SLUGS = """
        select
            c.id as category_id,
            c.created_at,
            c.updated_at,
            cs.*
        from
            platform_export.categories as c
        inner join
            export_etl.category_slugs as cs
        on  c.id = cs.id;
    """

    def drop_category_slugs(self) -> None:
        """
        Drops the `platform_export.category_slugs` table.
        """
        self.drop_table(table=self.CATEGORY_SLUGS_TABLE.full_name, cascade=True)

    def create_category_slugs(self) -> None:
        """
        Re-creates the `platform_export.category_slugs` table.
        """
        self.call(self.CREATE_CATEGORY_SLUGS)

    def select_category_slugs(self) -> DataFrameWithSchema:
        """
        Selects category slugs with their levels.

        :returns: typed DataFrameWithSchema
        """
        dataframe = self.select_to_df(sql=self.SELECT_CATEGORY_SLUGS, schema=CATEGORY_SLUGS_SCHEMA)

        return DataFrameWithSchema(dataframe=dataframe, schema=CATEGORY_SLUGS_SCHEMA)

    def select_countries(self) -> DataFrameWithSchema:
        """
        Selects all configured countries.

        :returns: typed DataFrameWithSchema
        """
        dataframe = self.select_to_df(sql=self.COUNTRIES_VIEW, schema=COUNTRIES_SCHEMA)

        return DataFrameWithSchema(dataframe=dataframe, schema=COUNTRIES_SCHEMA)
