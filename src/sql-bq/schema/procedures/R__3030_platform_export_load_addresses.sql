-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_platform_addresses;
create or replace procedure analytics_transformed.load_platform_addresses (
) options (
    description = """Loads `analytics_transformed.addresses` target table
from the `platform_export.addresses` staging table."""
)
begin
    merge into analytics_transformed.addresses as t
    using (
        with staging as (
            select
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from platform_export.addresses as t
        )

        select
            -- PK / time travel
            id,
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Mandatory address fields
            owner_id,
            first_name,
            family_name,
            country,
            post_code,
            town,
            street_name,
            house_no,
            phone,
            -- Optional address fields
            male,
            address_type,
            supplement,
            company,
            vatin,
            personal_vatin,
            -- Calculated fields
            coalesce(male is null, false) as is_company,
            case
                when male then "Male"
                when not male then "Female"
                else "Company"
            end as gender
        from staging
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            owner_id = s.owner_id,
            first_name = s.first_name,
            family_name = s.family_name,
            country = s.country,
            post_code = s.post_code,
            town = s.town,
            street_name = s.street_name,
            house_no = s.house_no,
            phone = s.phone,
            male = s.male,
            address_type = s.address_type,
            supplement = s.supplement,
            company = s.company,
            vatin = s.vatin,
            personal_vatin = s.personal_vatin,
            is_company = s.is_company,
            gender = s.gender
    ;
end;

drop procedure if exists analytics_transformed.check_addresses;
create or replace procedure analytics_transformed.check_addresses ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        valid_from,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.addresses
    group by all
    having count(*) > 1;
end;

/*
select * from platform_export.addresses limit 10;
truncate table analytics_transformed.addresses;
select * from analytics_transformed.addresses limit 10;

call analytics_transformed.load_platform_addresses ();
call analytics_transformed.check_addresses ();
*/
