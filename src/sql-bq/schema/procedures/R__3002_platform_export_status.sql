drop procedure if exists analytics_transformed.platform_export_status;
create procedure analytics_transformed.platform_export_status (table_name string)
/*
    Gets export status of the given `platform_table` returning:
    - last_id: last loaded `id`
    - last_modified: last `modified_at` timestamp

    Notes:
    - need a procedure as table function does not allow for use of if/case;
    - cannot use `case` as SIMBA driver in flyway and DataGrip is complaining
*/
begin
    declare snapshot_tables array<string> default [
        'attributes',
        'countries',
        'full_order_item_refunds',
        'instance_attribute_values',
        'offer_properties',
        'order_financial_revenue',
        'order_item_exchange_rate',
        'order_item_offers',
        'product_attribute_values',
        'product_categories',
        'product_rankings'
    ];
    declare sql string;

    if table_name in unnest(snapshot_tables) then
        -- fully loaded snapshot tables
        select
            null as last_id,
            null as last_modified;
    else
        -- incrementally loaded tables (incremental or time travel)
        set sql = format(
            '''
            select
                max(id) as last_id,
                max(modified_at) as last_modified
            from analytics_transformed.%s
            ''',
            table_name
        );

        execute immediate sql;
    end if;
end;

begin
    -- test me: Reload current partition for the day
    if ("${runTest}" = "true") then -- noqa
        -- Incremental table: max id & modified_at returned
        call analytics_transformed.platform_export_status ('products');

        -- Snapshot table: null row returned
        call analytics_transformed.platform_export_status ('attributes');

        -- Non-existing table: exception is raised
        begin
            call analytics_transformed.platform_export_status ('xyz');
        exception when error then
            select @@error.message as error_message;
        end;
    end if;
end;
