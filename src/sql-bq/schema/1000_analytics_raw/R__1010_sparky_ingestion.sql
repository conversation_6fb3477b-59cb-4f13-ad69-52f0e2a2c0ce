begin
    declare i int64 default 0;
    declare table_name string;
    declare drop_sql string;
    declare create_sql string;

    declare ingestion_tables array<string> default [
        'products',
        'categories',
        'merchants',
        'instances',
        'users',
        'orders',
        'order_items',
        'addresses',
        'exchange_rates',
        'offers',
        'shipping_profiles',
        'shipping_profile_destinations',
        'addons',
        'attributes',
        'countries',
        'order_item_refunds',
        'order_transfers_hyperwallet',
        'order_transfer_reversals',
        'order_transfers',
        'instance_attribute_values',
        'offer_properties',
        'product_attribute_values',
        'product_categories',
        'product_rankings'
    ];

    declare sql_template string default '''
    create external table analytics_raw.<table_name>
        with partition columns (run_id string)
        options (
        format = 'PARQUET',
        uris = ['gs://analytics-pipelines-staging-raw/platform_ingestion/ingestion.<table_name>/*.parquet'],
        hive_partition_uri_prefix = 'gs://analytics-pipelines-staging-raw/platform_ingestion/ingestion.<table_name>',
        require_hive_partition_filter = true,
        description = 'External table mapped to parquet files and triggered from Sparky ingestion job. Owner: data_engineering'
    );
    ''';

    if "${skipIngestionTables}" = "false" then -- noqa
        while i < array_length(ingestion_tables)
        do
            set table_name = ingestion_tables[ordinal(i + 1)];
            set drop_sql = 'drop external table if exists analytics_raw.' || table_name || ';';
            execute immediate drop_sql;
            set create_sql = replace(sql_template, '<table_name>', table_name);
            execute immediate create_sql;
            set i = i + 1;
        end while;
    end if;
end;
