import logging
from dataclasses import dataclass, field
from functools import cached_property
from pathlib import Path
from typing import Optional

from dataclasses_json import DataClassJsonMixin

from common.time_service import TimeService
from common.utils import get_run_id, to_kebab_case

# sh uses a wrapper over logging.Logger, its logger names are "sh.<smth>"
# see sh.Logger.__init__ : https://github.com/amoffat/sh/blob/master/sh.py#L608
logging.getLogger("sh").setLevel(logging.WARNING)


@dataclass(frozen=True)
class PgParams(DataClassJsonMixin):
    """
    pg_dump and pg_restore common params
    Ref. https://www.postgresql.org/docs/15/app-pgdump.html
         https://www.postgresql.org/docs/15/app-pgrestore.html
    host: DB host name or ip, defaults to localhost IP
    format: Format of the output, default 'directory' means a directory with one compressed file
            for each table and blob being dumped, plus a so-called Table of Contents file
            describing the dumped objects in a machine-readable format that pg_restore can read.
    jobs: Run the dump/restore in parallel by dumping tables simultaneously.
          This option may reduce the time needed to perform the dump but it also increases
          the load on the database server. You can only use this option with the directory output format
          because this is the only output format where multiple processes can write their data at the same time.
          It will open jobs + 1 connections to the database.
    schema: List of schemas to be dumped.
    clean: Output commands to DROP all the dumped database objects prior to outputting the commands for creating them.
           This option is useful when the restore is to overwrite an existing database.
           If any of the objects do not exist in the destination database,
           ignorable error messages will be reported during restore, unless --if-exists is also specified.
    if_exists: Use DROP ... IF EXISTS commands to drop objects in --clean mode.
               This suppresses “does not exist” errors that might otherwise be reported.
               This option is not valid unless --clean is also specified.
    no_privileges: Prevent dumping/restoring of access privileges (grant/revoke commands).
    no_owner: Do not output commands to set ownership of objects to match the original database.
    verbose:  This will cause to output detailed info and progress messages to standard error.
    """

    host: str = "127.0.0.1"
    format: str = "directory"
    jobs: int = 4
    schema: list[str] = field(default_factory=lambda: ["public", "zendesk"])
    clean: bool = True
    if_exists: bool = True
    no_privileges: bool = True
    no_owner: bool = True
    verbose: bool = True

    @cached_property
    def command_params(self) -> list[str]:
        """
        Prepares pg_dump and pg_restore command line params
        """
        params = []
        for key, value in self.to_dict().items():
            if key != "host":
                pg_key = f"--{to_kebab_case(key)}"
                if isinstance(value, list):
                    for v in value:
                        params.extend([pg_key, v])
                elif isinstance(value, bool):
                    if value:
                        params.append(pg_key)
                else:
                    params.extend([pg_key, value])

        return params


class DumpRestoreDb:
    """
    Python base class for DumpDb and RestoreDb
    """

    def __init__(
        self,
        dump_base_path: Path,
        time_service: Optional[TimeService] = None,
    ) -> None:
        self._dump_base_path = dump_base_path
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""
        return get_run_id(self._time_service.now)

    @cached_property
    def dump_full_path(self) -> Path:
        """Dump full location"""

        return Path(f"{self._dump_base_path}_{self.run_id}")

    @staticmethod
    def _filter_to_pg_errors(stderr: str) -> str:
        """
        Filters pg command standard error response to the error and warning lines.
        This is needed since GCP Log entry cannot exceed maximum size of 256.0K.
        Ref. https://cloud.google.com/logging/quotas
        """
        errors = {line for line in stderr.split("\\n") if any(s in line.lower() for s in ["error", "warning"])}

        return "\n".join(errors)
