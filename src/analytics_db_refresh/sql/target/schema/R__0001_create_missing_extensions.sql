-- Flyway runs this script on every migration: ${flyway:timestamp}
-- noqa: disable=LXR,PRS
create extension if not exists isn cascade;
alter extension isn set schema ${public_temp_schema};

create extension if not exists ltree cascade;
alter extension ltree set schema ${public_temp_schema};

create extension if not exists "uuid-ossp" cascade;
alter extension  "uuid-ossp" set schema ${public_temp_schema};

-- to workaround error: must be superuser to set schema of text search template unaccent
-- related data is not cleaned e.g. search_data column in public.addresses table
drop extension if exists unaccent;
create extension unaccent schema ${public_temp_schema} cascade;

create extension if not exists pg_trgm cascade;
alter extension pg_trgm set schema ${public_temp_schema};

drop extension if exists tablefunc;
create extension tablefunc schema ${public_temp_schema} cascade;
-- noqa: enable=all
