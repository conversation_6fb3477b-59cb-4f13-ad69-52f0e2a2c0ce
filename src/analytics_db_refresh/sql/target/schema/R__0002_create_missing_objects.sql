-- Flyway runs this script on every migration: ${flyway:timestamp}
do
$$
    begin
        if not exists
            (select *
             from pg_type
             where typname = 'currency' and typnamespace::regnamespace::text = '${public_temp_schema}') then
            create type ${public_temp_schema}.currency as enum ('FOO');
        end if;

        if not exists
            (select *
             from pg_type
             where typname = 'monetary' and typnamespace::regnamespace::text = '${public_temp_schema}') then
            create domain ${public_temp_schema}.monetary as numeric(14, 4);
        end if;

    end
$$;
