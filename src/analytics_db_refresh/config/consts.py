import os
from pathlib import Path

from common.cloud_sql_instance_client.instance_model import (
    DatabaseFlag,
    MaintenanceWindow,
)

"""Common consts for all environments."""

PLATFORM_SQL_INSTANCE = "platform-19af-15"
PLATFORM_PROJECT_NAME = "refb-platform-production"

DUMP_BASE_PATH = Path("/tmp/dumps/platform_dump")

SQL_MIGRATIONS_PATH = Path(os.environ.get("SQL_MIGRATIONS_PATH", "../sql-pg")).resolve()
SQL_COMMON_MIGRATIONS_PATH = Path("./sql/common").resolve()
SQL_SOURCE_MIGRATIONS_PATH = Path("./sql/source").resolve()
SQL_TARGET_MIGRATIONS_PATH = Path("./sql/target").resolve()

db_flags = [DatabaseFlag(name="autovacuum", value="off")]
db_maintenance_window = MaintenanceWindow(day=7, hour=20)
