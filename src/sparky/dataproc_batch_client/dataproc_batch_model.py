from dataclasses import dataclass, field
from typing import Dict, List

from dataclasses_json import DataClassJsonMixin


@dataclass(frozen=True)
class Timestamp(DataClassJsonMixin):
    seconds: int
    nanos: int


@dataclass(frozen=True)
class PysparkBatch(DataClassJsonMixin):
    main_python_file_uri: str


@dataclass(frozen=True)
class RuntimeInfoApproximateUsage(DataClassJsonMixin):
    milli_dcu_seconds: int
    shuffle_storage_gb_seconds: int
    accelerator_type: str


@dataclass(frozen=True)
class RuntimeInfo(DataClassJsonMixin):
    output_uri: str
    approximate_usage: RuntimeInfoApproximateUsage


@dataclass(frozen=True)
class RuntimeConfig(DataClassJsonMixin):
    version: str
    container_image: str
    properties: Dict[str, str]


@dataclass(frozen=True)
class Ttl(DataClassJsonMixin):
    seconds: int


@dataclass(frozen=True)
class ExecutionConfig(DataClassJsonMixin):
    service_account: str
    subnetwork_uri: str
    ttl: Ttl


@dataclass(frozen=True)
class EnvironmentConfig(DataClassJsonMixin):
    execution_config: ExecutionConfig


@dataclass(frozen=True)
class StateHistory(DataClassJsonMixin):
    state: str
    state_start_time: Timestamp


@dataclass(frozen=True)
class Batch2(DataClassJsonMixin):
    name: str
    uuid: str
    create_time: Timestamp
    pyspark_batch: PysparkBatch
    runtime_info: RuntimeInfo
    state: str
    state_time: Timestamp
    creator: str
    labels: Dict[str, str]
    runtime_config: RuntimeConfig
    environment_config: EnvironmentConfig
    operation: str
    state_history: List[StateHistory] = field(default_factory=list)
