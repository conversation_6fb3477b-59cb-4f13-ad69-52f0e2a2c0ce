import dataclasses
from dataclasses import dataclass, field
from functools import cached_property
from typing import Dict, Optional, TypeAlias

from google.api_core.exceptions import NotFound
from google.cloud import dataproc_v1
from google.cloud.dataproc_v1 import Batch

from common.config import Config

BatchState: TypeAlias = Batch.State


@dataclasses.dataclass(frozen=True)
class DataprocBatchClientConfig(Config):
    """
    - sleep_in_seconds: number of seconds to sleep while waiting for SQL operation to complete
    """

    sleep_in_seconds: int = 10


@dataclass(frozen=True, kw_only=True)
class BatchProperties:
    batch_id: str
    entry_point: str = "main.py"
    container_image: str
    subnet: str = "default"
    version: str = "2.2"
    service_account: str
    properties: Dict[str, str] = field(default_factory=dict)

    @cached_property
    def full_entry_point(self) -> str:
        return f"file:///opt/python/packages/{self.entry_point}"


class DataprocBatchClient:
    """
    Dataproc Batch client
    Ref. https://cloud.google.com/dataproc-serverless/docs/reference/rest/v1/projects.locations.batches
    """

    @cached_property
    def api_path(self) -> str:
        return f"projects/{self._config.project_id}/locations/{self._config.location}"

    def __init__(self, config: DataprocBatchClientConfig):
        self._config = config

        self._client = dataproc_v1.BatchControllerClient(
            client_options={"api_endpoint": f"{self._config.location}-dataproc.googleapis.com"}
        )

    def list_batches(self) -> list[Batch]:
        request = dataproc_v1.ListBatchesRequest(parent=self.api_path)  # noqa

        page_result = self._client.list_batches(request=request)

        return [response for response in page_result]

    def describe_batch(self, batch_id: str) -> Optional[Batch]:
        """
        Returns the Batch object for the given batch id.
        Args:
            batch_id (str): The batch id (not the full resource name), e.g. 'sparky'.
        Returns:
            Batch: The Batch object, or None if not found.
        """

        if self.api_path in batch_id:
            name = batch_id
        else:
            name = f"{self.api_path}/batches/{batch_id}"

        try:
            request = dataproc_v1.GetBatchRequest(name=name)  # noqa
            return self._client.get_batch(request=request)
        except NotFound:
            return None

    def is_batch_running(self, batch_id: str) -> bool:
        """
        Returns True if the batch with the given id is in a running state.
        Args:
            batch_id (str): The batch id (not the full resource name), e.g. 'sparky'.
        Returns:
            bool: True if running, False otherwise.
        """
        batch = self.describe_batch(batch_id)
        if not batch:
            return False

        return batch.state in (BatchState.PENDING, BatchState.RUNNING, BatchState.CANCELLING)

    def create_batch(self, properties: BatchProperties) -> Batch:
        """
        Creates a Dataproc batch job using the provided properties.
        Args:
            properties (BatchProperties): The batch properties.
        Returns:
            Batch: The created Batch object.
        """

        batch = dataproc_v1.Batch(
            pyspark_batch=dataproc_v1.PySparkBatch(main_python_file_uri=properties.full_entry_point),
            runtime_config=dataproc_v1.RuntimeConfig(
                version=properties.version, container_image=properties.container_image, properties=properties.properties
            ),
            environment_config=dataproc_v1.EnvironmentConfig(
                execution_config=dataproc_v1.ExecutionConfig(
                    service_account=properties.service_account, subnetwork_uri=properties.subnet
                )
            ),
        )
        operation = self._client.create_batch(parent=self.api_path, batch=batch, batch_id=properties.batch_id)

        return operation.result()  # Waits for batch creation and returns the Batch object


if __name__ == "__main__":
    client = DataprocBatchClient(DataprocBatchClientConfig())
    # batch = client.describe_batch(batch_id="sparky")
    # client.is_batch_running(batch_id=batch.name)
    # print(batch)1
    props = BatchProperties(
        batch_id="sparky",
        container_image="europe-west3-docker.pkg.dev/refb-analytics-staging/analytics-pipelines-artifacts/sparky",
        service_account="<EMAIL>",
        properties={
            "spark.dataproc.driver.compute.tier": "premium",
            "spark.dataproc.executor.compute.tier": "premium",
            "spark.driver.memory": "4g",
            "spark.driver.cores": "4",
            "spark.executor.memory": "4g",
            "spark.executor.cores": "4",
            "spark.dynamicAllocation.enabled": "true",
            "spark.reducer.fetchMigratedShuffle.enabled": "true",
            "spark.dynamicAllocation.initialExecutors": "10",
            "spark.dynamicAllocation.maxExecutors": "10",
            "spark.dynamicAllocation.executorAllocationRatio": "1.0",
            "spark.decommission.maxRatio": "0.5",
            "spark.jars": "/opt/python/packages/spark/jars/postgresql-42.7.3.jar",
        },
    )

    batch = client.create_batch(props)
