from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from functools import cached_property
from logging import Logger
from typing import Optional

from pyspark.sql import <PERSON>Frame, DataFrameReader, SparkSession
from spark.logger import get_spark_logger
from spark.utils import spark_timing
from sparky_model import IngestionTable, Partition, SparkyConfig

from common.config import DatabaseConfig
from common.time_service import TimeService
from common.utils import get_run_id


class Sparky:
    def __init__(
        self,
        config: SparkyConfig,
        database_config: DatabaseConfig,
        spark_session: SparkSession,
        time_service: Optional[TimeService] = None,
    ):
        self._config = config
        self._spark = spark_session
        self._db_config = database_config
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def logger(self) -> Logger:
        return get_spark_logger(spark_session=self._spark, name=self.app_name)

    @cached_property
    def config(self) -> SparkyConfig:
        return self._config

    @cached_property
    def app_name(self) -> str:
        return self._spark.conf.get("spark.app.name")

    @cached_property
    def run_id(self) -> str:
        return get_run_id(self._time_service.now)

    @spark_timing(message="sparky")
    def run(self) -> None:
        """
        Run the spark application.
        """

        self.logger.info(f"Starting the job '{self.app_name}' with run id: '{self.run_id}'...")
        self.logger.info(f"Connecting to the Cloud SQL instance: '{self._db_config.jdbc_connection_url}' ...")
        self.logger.info(f"Ingesting {[table.full_name for table in self.config.ingestion_tables]} table(s)...")

        workers = len(self.config.ingestion_tables) if self.config.run_in_parallel else 1
        if self.config.run_in_parallel:
            with ThreadPoolExecutor(max_workers=workers) as executor:
                futures = {executor.submit(self.ingest, table) for table in self.config.ingestion_tables}
                _ = [future.result() for future in as_completed(futures)]
        else:
            for table in self.config.ingestion_tables:
                self.ingest(table)

    def ingest(self, table: IngestionTable) -> None:
        """
        Ingest table to parquet.
        :param table: table to ingest
        """

        self.logger.info(f"Reading '{table.full_name}' table...")
        df = self.load_to_dataframe(table)

        location = self.file_location(table)
        self.logger.info(f"Writing to '{location}'...")
        df.write.mode("overwrite").parquet(location)
        df.unpersist()

        self.logger.info(f"Table '{table.full_name}' saved in '{location}'.")

    def load_to_dataframe(self, table: IngestionTable) -> DataFrame:
        """
        Load table to dataframe.
        If raed_at_once is True, read the table at once.
        Otherwise, calculate partition and read the table in parallel.
        :param table: table to load
        """

        jdbc_reader = (
            self._spark.read.format("jdbc")
            .option("url", self._db_config.jdbc_connection_url)
            .option("driver", "org.postgresql.Driver")
            .option("dbtable", table.query)
            .option("user", self._db_config.username)
            .option("password", self._db_config.password)
        )

        if table.read_at_once:
            df = jdbc_reader.load()
        else:
            self.logger.info(f"Calculating partition for '{table.full_name}'...")
            partition = self.calculate_partition(jdbc_reader, table)
            self.logger.info(f"Partitioning '{table.full_name}' by following partition: '{partition}' ...")
            df = (
                jdbc_reader.option("partitionColumn", partition.column)
                .option("dbtable", table.query)
                .option("lowerBound", partition.lower_bound)
                .option("upperBound", partition.upper_bound)
                .option("numPartitions", partition.number)
                .load()
            )

        return df

    def calculate_partition(self, reader: DataFrameReader, table: IngestionTable) -> Partition:
        """
        Calculate partition for the given table.
        If number of partitions is not specified, it is calculated based on the number of rows.
        """

        bounds = reader.option("dbtable", f"({table.partition_query}) AS bounds").load().collect()

        lower_bound: Optional[int] = next((row["lower_bound"] for row in bounds), None)
        upper_bound: Optional[int] = next((row["upper_bound"] for row in bounds), None)

        if not upper_bound or not lower_bound:
            lower_bound = 0
            upper_bound = 1
            self.logger.warning(
                f"Lower or upper bound is empty for '{table.full_name}', "
                f"falling back to default values {lower_bound=} and {upper_bound=}!"
            )

        rows_count = upper_bound - lower_bound
        partition_number = (
            table.estimate_partition_number(rows_count) if not table.partition.number else table.partition.number
        )

        return Partition(
            column=table.partition.column,
            lower_bound=lower_bound,
            upper_bound=upper_bound,
            number=partition_number,
        )

    def file_location(self, table: IngestionTable) -> str:
        """
        Calculate file location for the given table.
        """
        return f"{self.config.file_location_prefix}/{table.full_name}/run_id={self.run_id}"
