from config_provider import provide, set_local_cloud
from spark.spark import spark_builder

from common.config import DEFAULT_CONFIG
from sparky import Sparky


def submit(run_locally_on_cloud: bool = False) -> None:
    """
    Run the Sparky job, depending on the environment configuration.
    :param run_locally_on_cloud: whether to run the job locally, but connect to the Cloud SQL instance and
                          get the refresh status from the staging BigQuery tables.
                          By default, it's False, so you have pass it explicitly.
    """
    if run_locally_on_cloud:
        set_local_cloud()

    config = provide(env_config=DEFAULT_CONFIG)

    with spark_builder(app_name=Sparky.__name__) as spark_session:
        sparky = Sparky(
            config=config.sparky_config,
            database_config=config.db_config,
            spark_session=spark_session,
        )

        sparky.run()


if __name__ == "__main__":
    # DataProc Batches entry point
    submit()
