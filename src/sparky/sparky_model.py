import math
from dataclasses import dataclass
from datetime import UTC, datetime
from enum import IntEnum
from functools import cached_property
from typing import Optional

from dataclasses_json import DataClassJsonMixin

from common.config import Config
from common.sql_model import SqlTable

CUT_OFF_DATETIME = datetime(2017, 1, 1, tzinfo=UTC)


@dataclass(frozen=True)
class IngestionStatus(DataClassJsonMixin):
    """
    Ingestion status / change tracking columns:
    https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    last_modified: datetime = CUT_OFF_DATETIME
    last_id: int = 0


class IngestionType(IntEnum):
    """
    Ingestion types:

    - INCREMENTAL: uses `id`, `updated_at`, `deleted_at` fields to identify data increment to load
    - TIME_TRAVEL: uses `id`, `valid_from`, `valid_to` fields to identify data increment to load
    - SNAPSHOT: always exports full data snapshot

    Ref.: https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    INCREMENTAL = 1
    TIME_TRAVEL = 2
    SNAPSHOT = 3


@dataclass(frozen=True)
class Partition:
    column: str = "id"
    lower_bound: Optional[int] = None
    upper_bound: Optional[int] = None
    number: Optional[int] = None


@dataclass(frozen=True, kw_only=True)
class IngestionTable(SqlTable):
    schema_name: str = "ingestion"
    partition: Optional[Partition] = None
    ingestion_type: IngestionType = IngestionType.INCREMENTAL
    status: Optional[IngestionStatus] = None
    condition: Optional[str] = None

    @cached_property
    def read_at_once(self) -> bool:
        """Whether to read the table at once"""
        return self.partition is None

    @cached_property
    def query(self) -> str:
        """
        Returns query to read data from the table.
        """

        full_name = f"{self.schema_name}.{self.table_name}"
        if self.condition:
            return f"(select * from {full_name} where {self.condition}) as {self.table_name}"

        return full_name

    @cached_property
    def partition_query(self) -> Optional[str]:
        """
        Returns query to calculate partition bounds.
        """
        if not self.partition:
            return None

        return f"""
            select
                min({self.partition.column}) as lower_bound,
                max({self.partition.column}) as upper_bound
                from {self.full_name} {f"where {self.condition}" if self.condition else ''}
            """

    @staticmethod
    def estimate_partition_number(rows_count: int) -> int:
        """
        A naive algorithm based on a trial-and-error approach to estimate the number of partitions.
        """

        # by default, one partition per 300k rows
        partition_number = math.ceil(rows_count / 300_000)

        # for big tables, increase the number of partitions by 10x
        if rows_count > 10_000_000:
            partition_number *= 10

        # at least one partition in the edge case
        return max(1, partition_number)


@dataclass(frozen=True, kw_only=True)
class SparkyConfig(Config):
    ingestion_tables: list[IngestionTable]
    run_in_parallel: bool = True

    @cached_property
    def bucket_prefix(self) -> str:
        return "gs://" if self.is_cloud else ""

    @cached_property
    def file_location_prefix(self) -> str:
        return f"{self.bucket_prefix}{self.raw_bucket}/platform_ingestion"
