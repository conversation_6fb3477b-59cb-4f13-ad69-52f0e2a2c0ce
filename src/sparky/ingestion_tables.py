from sparky_model import IngestionTable, IngestionType, Partition

partition = Partition()

ingestion_tables = [
    # INCREMENTAL:
    IngestionTable(table_name="products", ingestion_type=IngestionType.INCREMENTAL),
    IngestionTable(table_name="categories", ingestion_type=IngestionType.INCREMENTAL),
    IngestionTable(table_name="merchants", ingestion_type=IngestionType.INCREMENTAL),
    IngestionTable(table_name="instances", ingestion_type=IngestionType.INCREMENTAL),
    IngestionTable(table_name="users", ingestion_type=IngestionType.INCREMENTAL),
    IngestionTable(table_name="orders", ingestion_type=IngestionType.INCREMENTAL, partition=partition),
    IngestionTable(table_name="order_items", ingestion_type=IngestionType.INCREMENTAL, partition=partition),
    # TIME_TRAVEL:
    IngestionTable(table_name="addresses", ingestion_type=IngestionType.TIME_TRAVEL, partition=partition),
    IngestionTable(table_name="exchange_rates", ingestion_type=IngestionType.TIME_TRAVEL),
    IngestionTable(table_name="offers", ingestion_type=IngestionType.TIME_TRAVEL, partition=partition),
    IngestionTable(table_name="shipping_profiles", ingestion_type=IngestionType.TIME_TRAVEL),
    IngestionTable(table_name="shipping_profile_destinations", ingestion_type=IngestionType.TIME_TRAVEL),
    IngestionTable(table_name="addons", ingestion_type=IngestionType.TIME_TRAVEL),
    # SNAPSHOT:
    IngestionTable(table_name="attributes", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(table_name="countries", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(
        table_name="order_item_refunds",
        partition=Partition(column="order_item_refund_id"),
        ingestion_type=IngestionType.SNAPSHOT,
    ),
    IngestionTable(
        table_name="order_transfers_hyperwallet", ingestion_type=IngestionType.SNAPSHOT, partition=partition
    ),
    IngestionTable(table_name="order_transfer_reversals", ingestion_type=IngestionType.SNAPSHOT, partition=partition),
    IngestionTable(table_name="order_transfers", ingestion_type=IngestionType.SNAPSHOT, partition=partition),
    IngestionTable(table_name="instance_attribute_values", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(table_name="offer_properties", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(table_name="product_attribute_values", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(table_name="product_categories", ingestion_type=IngestionType.SNAPSHOT),
    IngestionTable(table_name="product_rankings", ingestion_type=IngestionType.SNAPSHOT),
]
