#!/usr/bin/env bash
set -euo pipefail

echo "Building sparky image..."
make deploy-staging args="-target=module.sparky_image -var=force_deploy=true"

echo "Submitting dataproc batch..."
gcloud dataproc batches delete sparky --region=europe-west3 --quiet 2>/dev/null || true

# shellcheck disable=SC2140
gcloud dataproc batches submit pyspark file:///opt/python/packages/main.py \
--async \
--container-image=europe-west3-docker.pkg.dev/refb-analytics-staging/analytics-pipelines-artifacts/sparky \
--region=europe-west3 \
--batch=sparky \
--subnet=default \
--version=2.2 \
--service-account=<EMAIL> \
--properties="spark.dataproc.driver.compute.tier"="premium",\
"spark.dataproc.executor.compute.tier"="premium",\
"spark.driver.memory"="4g",\
"spark.driver.cores"="4",\
"spark.executor.memory"="4g",\
"spark.executor.cores"="4",\
"spark.dynamicAllocation.enabled"="true",\
"spark.reducer.fetchMigratedShuffle.enabled"="true",\
"spark.dynamicAllocation.initialExecutors"="10",\
"spark.dynamicAllocation.maxExecutors"="10",\
"spark.dynamicAllocation.executorAllocationRatio"="1.0",\
"spark.decommission.maxRatio"="0.5",\
"spark.jars"="/opt/python/packages/spark/jars/postgresql-42.7.3.jar"


echo "Done!"
