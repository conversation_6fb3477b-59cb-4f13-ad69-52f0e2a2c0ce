# Platform data export into Analytics DWH

The solution is based on
the [PoC on incremental load](https://www.notion.so/refurbed/PoC-on-incremental-load-d81362e9da8c4a46ab2fcd4cebe5a2d5)
findings.

It was updated according
to [POC with federated queries to cloud-sql](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/557)
using federated queries to cloud-sql as a better alternative to in memory ingestion with `pandas.read_sql`.

It exports platform data from PG tables/views into BQ datasets that form a lakehouse:

- `platform_export` - incremental export of platform data into BQ staging tables;
- `analytics_transformed` - target native BQ tables with data merged daily;

## High level architecture

```mermaid
sequenceDiagram
    loop FOR EVERY SOURCE
        ETL job ->> BQ (analytics_staging): Get data changes from cloud-sql
        Note right of ETL job: Using federated queries
        ETL job ->> BQ (analytics_transformed): MERGE changes into target table
        Note right of ETL job: Using BQ/SQL Merge
    end
```

The process is triggered by the [platform-export](/infra/workflows/platform_export.yaml) workflows and consists of the
following components:

**Dispatcher**

Cloud Run Function (512Mi), which returns PG source name to be extracted;

**ETL**

- Cloud Run Job per source that:
    - Extracts given export view as a snapshot/increment into BQ `platform_export` staging dataset;
    - Loads extracted data into BQ `analytics_transformed` dataset by sql procedure;

## Current scope

The source is `analytics` database and is dependent on daily analytics database refresh.
Currently configured sources are configured
in [export_dispatcher](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/blob/27f8011ecec7033b56c84c693f98412d8ac25cc7/src/platform_export/dispatcher/export_dispatcher.py#L31).

| BQ `analytics_transformed` table |                          PG export view/table | Logical Size | Physical Size |
|:---------------------------------|----------------------------------------------:|-------------:|--------------:|
| addresses                        |                     platform_export.addresses |      3.24 GB |        741 MB |
| attributes                       |                         export_etl.attributes |     14.85 MB |        3.8 MB |
| categories                       |                         export_etl.categories |     24.66 KB |      11.46 KB |
| countries                        |                     platform_export.countries |        753 B |       2.89 KB |
| exchange_rates                   |                platform_export.exchange_rates |      7.28 MB |       2.91 MB |
| full_order_item_refunds          |            export_etl.full_order_item_refunds |    286.14 MB |       31.9 MB |
| instance_attribute_values        |     platform_export.instance_attribute_values |     62.72 MB |       6.31 MB |
| instances                        |                     platform_export.instances |     55.73 MB |       8.42 MB |
| merchants                        |                     platform_export.merchants |    269.24 KB |     235.76 KB |
| offers                           |                        platform_export.offers |     25.24 GB |       2.75 GB |
| offer_properties                 |                   export_etl.offer_properties |     30.91 MB |        8.7 MB |
| order_financial_revenue          |             analytics.order_financial_revenue |      1.84 GB |     272.72 MB |
| order_item_exchange_rate         |            analytics.order_item_exchange_rate |    435.83 MB |      50.15 MB |
| order_item_offers                |                  export_etl.order_item_offers |      2.41 GB |     758.62 MB |
| order_items                      |                   platform_export.order_items |     10.68 GB |     834.57 MB |
| orders                           |                        platform_export.orders |       2.5 GB |     699.39 MB |
| product_attribute_values         |      platform_export.product_attribute_values |      7.97 MB |     837.05 KB |
| product_categories               |                 export_etl.product_categories |      1.69 MB |     172.38 KB |
| product_rankings                 |              platform_export.product_rankings |      9.49 MB |       3.23 MB |
| products                         |                           export_etl.products |      4.31 MB |       1.66 MB |
| shipping_profile_destinations    | platform_export.shipping_profile_destinations |      2.85 MB |      661.6 KB |
| shipping_profiles                |             platform_export.shipping_profiles |      2.15 MB |     792.42 KB |
| users                            |                         platform_export.users |    518.44 MB |     210.78 MB |

## Sub-hour data export

In the context
of [Sub-hour data freshness](https://www.notion.so/refurbed/Sub-hour-data-freshness-224b6a8983ab80fabccad5e6a9dc5034#224b6a8983ab80ad9077c733d5bf8b4f).

**Data sources are ready for sub-hour data export:**
All views from `export_etl` or `platform_export` schema as they have dependency on the `public` schema only.

**Data sources not ready for sub-hour data export:**

- export_etl.order_item_offers
- analytics.order_item_exchange_rate
- analytics.order_financial_revenue

There is dependency on the `public.order_item_offers` that cannot be satisfied from the view atm.

### Step 0

1. Create a clone of production database server to run ETL jobs against it.
2. Run flyway migrations to create `export_etl` schema with the export views.

### Step 1

1. Create separate BQ external connections for sub-hour (platform_export) and daily (analytics) exports.
2. Create separate schedules for daily and sub-hour exports.
3. Re-direct source to the relevant connection in the `PlatformExportEtl.extract` depending on the schedule.

### Step 2

**Assumption:** we got full history of `offers` in BQ.

1. Build `order_item_offers` from the source tables in a view or BQ procedure.
2. Rebuilt the 3 sources in question with the new view/procedure.
3. Run platform export every hour from the single schedule.

## How to extend platform export with new source

1. Create new `view` in `analytics` database and `platform_export` schema:

- ensure to cast all PG custom types to primitive types accepted by BQ,
- measure size of the source tables using `pg_relation_size`,
- check what is the primary key,

2. Create relevant entries in the `export_target.py` file:

- SOURCE view name,
- TARGET table name,
- add mapping to `SOURCE_TO_TARGET` variable

3. Create BQ staging table in `platform_export` dataset

4. Create BQ target table in `analytics_transformed` dataset:

- always add primary key constraint,
- if relevant, add foreign key constraints,
- if table is over 10 GB consider partitioning,
- always add `cluster by` with columns frequently used in filters,

5. Create load procedure in `analytics_transformed` dataset:

- use `MERGE` statement for incremental and time travel tables,
- use `TRUNCATE` and `INSERT` for fully reloaded tables,
- add related `check` procedure that checks for data anomalies,

6. Run integration test to ensure ETL is working as expected:

- run `test_platform_export` in `tests/integration` directory
