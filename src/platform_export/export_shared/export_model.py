import os
from dataclasses import dataclass
from datetime import UTC, datetime
from enum import IntEnum
from functools import cached_property
from os import environ
from typing import NamedTuple, Self

from dataclasses_json import DataClassJsonMixin

from common.config import Config
from common.pipeline_logging.pipeline_model import (
    SupportsPipelineExecution,
    SupportsPipelineRun,
)
from common.sql_model import SqlTable
from common.typings import TimestampInSec

CUT_OFF_DATETIME = datetime(2023, 1, 1, tzinfo=UTC)


@dataclass(frozen=True, kw_only=True)
class ExportConfig(Config, SupportsPipelineRun):
    """
    Common export config defining mapping between source platform view and destination BQ table.
    The Default configuration is passed via environment variables.

    :timestamp: processing timestamp used later for prefixing extract in GCS
    :source: source SQL table/view being ingested
    """

    EXPORT_TIMESTAMP = "EXPORT_TIMESTAMP"
    EXPORT_SOURCE = "EXPORT_SOURCE"

    timestamp: TimestampInSec = int(environ.get(EXPORT_TIMESTAMP, 1737460800))
    source: SqlTable = SqlTable.from_full_name(environ.get(EXPORT_SOURCE, "export_etl.products"))

    @cached_property
    def pipeline_name(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.PIPELINE_NAME, "platform-export")

    @cached_property
    def run_id(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.RUN_ID, "")

    @classmethod
    def dumps(cls, items: list[Self]) -> str:
        """
        Encodes into a JSON array instances of `ExportConfig`.
        Ref.: https://pypi.org/project/dataclasses-json/

        :param items: instances of `ExportConfig`
        :returns: json encoded string
        """
        return cls.schema().dumps(items, many=True)


@dataclass(frozen=True)
class ExportStatus(DataClassJsonMixin):
    """
    Export status / change tracking columns:
    https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    last_id: int = 0
    last_modified: datetime = CUT_OFF_DATETIME


class ExportType(IntEnum):
    """
    Export types:

    - INCREMENTAL: uses `id`, `updated_at`, `deleted_at` fields to identify data increment to load
    - TIME_TRAVEL: uses `id`, `valid_from`, `valid_to` fields to identify data increment to load
    - SNAPSHOT: always exports full data snapshot

    Ref.: https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    INCREMENTAL = 1
    TIME_TRAVEL = 2
    SNAPSHOT = 3


class ExportTarget(NamedTuple):
    """
    Export target definition:
    - table_name: BigQuery table name
    - export_type: type of export to be used for the table
    """

    table_name: str
    export_type: ExportType
