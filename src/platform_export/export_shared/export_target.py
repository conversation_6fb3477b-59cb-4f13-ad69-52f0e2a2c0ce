"""
SOURCE Postgres table/view to TARGET BigQuery table mapping.
"""

from export_shared.export_model import ExportTarget, ExportType

from common.sql_model import SqlTable

##############################################################
# Data sources not ready for sub-hour data export (3):
# due to dependency on daily refresh `public.order_item_offers`
##############################################################
ORDER_ITEM_OFFERS_SOURCE = SqlTable(schema_name="export_etl", table_name="order_item_offers")
ORDER_ITEM_OFFERS_TARGET = "order_item_offers"

ORDER_ITEM_EXCHANGE_RATE_SOURCE = SqlTable(schema_name="analytics", table_name="order_item_exchange_rate")
ORDER_ITEM_EXCHANGE_RATE_TARGET = "order_item_exchange_rate"

ORDER_FINANCIAL_REVENUE_SOURCE = SqlTable(schema_name="analytics", table_name="order_financial_revenue")
ORDER_FINANCIAL_REVENUE_TARGET = "order_financial_revenue"

###############################################
# Data sources ready for sub-hour data export:
###############################################
ADDRESSES_SOURCE = SqlTable(schema_name="platform_export", table_name="addresses")
ADDRESSES_TARGET = "addresses"

PRODUCTS_SOURCE = SqlTable(schema_name="export_etl", table_name="products")
PRODUCTS_TARGET = "products"

CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="categories")
CATEGORIES_TARGET = "categories"

MERCHANTS_SOURCE = SqlTable(schema_name="platform_export", table_name="merchants")
MERCHANTS_TARGET = "merchants"

INSTANCES_SOURCE = SqlTable(schema_name="platform_export", table_name="instances")
INSTANCES_TARGET = "instances"

EXCHANGE_RATES_SOURCE = SqlTable(schema_name="platform_export", table_name="exchange_rates")
EXCHANGE_RATES_TARGET = "exchange_rates"

ATTRIBUTES_SOURCE = SqlTable(schema_name="export_etl", table_name="attributes")
ATTRIBUTES_TARGET = "attributes"

INSTANCE_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="platform_export", table_name="instance_attribute_values")
INSTANCE_ATTRIBUTE_VALUES_TARGET = "instance_attribute_values"

PRODUCT_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="platform_export", table_name="product_attribute_values")
PRODUCT_ATTRIBUTE_VALUES_TARGET = "product_attribute_values"

SHIPPING_PROFILES_SOURCE = SqlTable(schema_name="platform_export", table_name="shipping_profiles")
SHIPPING_PROFILES_TARGET = "shipping_profiles"

SHIPPING_PROFILE_DESTINATIONS_SOURCE = SqlTable(
    schema_name="platform_export", table_name="shipping_profile_destinations"
)
SHIPPING_PROFILE_DESTINATIONS_TARGET = "shipping_profile_destinations"

OFFERS_SOURCE = SqlTable(schema_name="platform_export", table_name="offers")
OFFERS_TARGET = "offers"

ORDERS_SOURCE = SqlTable(schema_name="platform_export", table_name="orders")
ORDERS_TARGET = "orders"

ORDER_ITEMS_SOURCE = SqlTable(schema_name="platform_export", table_name="order_items")
ORDER_ITEMS_TARGET = "order_items"

FULL_ORDER_ITEM_REFUNDS_SOURCE = SqlTable("export_etl", "full_order_item_refunds")
FULL_ORDER_ITEM_REFUNDS_TARGET = "full_order_item_refunds"

COUNTRIES_SOURCE = SqlTable(schema_name="platform_export", table_name="countries")
COUNTRIES_TARGET = "countries"

PRODUCT_CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="product_categories")
PRODUCT_CATEGORIES_TARGET = "product_categories"

OFFER_PROPERTIES_SOURCE = SqlTable(schema_name="export_etl", table_name="offer_properties")
OFFER_PROPERTIES_TARGET = "offer_properties"

PRODUCT_RANKINGS_SOURCE = SqlTable(schema_name="platform_export", table_name="product_rankings")
PRODUCT_RANKINGS_TARGET = "product_rankings"

USERS_SOURCE = SqlTable(schema_name="platform_export", table_name="users")
USERS_TARGET = "users"

# Mapping of source PG SQL views to their target export type and BQ table
SOURCE_TO_TARGET: dict[SqlTable, ExportTarget] = {
    # INCREMENTAL:
    CATEGORIES_SOURCE: ExportTarget(CATEGORIES_TARGET, ExportType.INCREMENTAL),
    INSTANCES_SOURCE: ExportTarget(INSTANCES_TARGET, ExportType.INCREMENTAL),
    MERCHANTS_SOURCE: ExportTarget(MERCHANTS_TARGET, ExportType.INCREMENTAL),
    ORDERS_SOURCE: ExportTarget(ORDERS_TARGET, ExportType.INCREMENTAL),
    ORDER_ITEMS_SOURCE: ExportTarget(ORDER_ITEMS_TARGET, ExportType.INCREMENTAL),
    PRODUCTS_SOURCE: ExportTarget(PRODUCTS_TARGET, ExportType.INCREMENTAL),
    USERS_SOURCE: ExportTarget(USERS_TARGET, ExportType.INCREMENTAL),
    # TIME_TRAVEL:
    ADDRESSES_SOURCE: ExportTarget(ADDRESSES_TARGET, ExportType.TIME_TRAVEL),
    EXCHANGE_RATES_SOURCE: ExportTarget(EXCHANGE_RATES_TARGET, ExportType.TIME_TRAVEL),
    OFFERS_SOURCE: ExportTarget(OFFERS_TARGET, ExportType.TIME_TRAVEL),
    SHIPPING_PROFILES_SOURCE: ExportTarget(SHIPPING_PROFILES_TARGET, ExportType.TIME_TRAVEL),
    SHIPPING_PROFILE_DESTINATIONS_SOURCE: ExportTarget(SHIPPING_PROFILE_DESTINATIONS_TARGET, ExportType.TIME_TRAVEL),
    # SNAPSHOT:
    ATTRIBUTES_SOURCE: ExportTarget(ATTRIBUTES_TARGET, ExportType.SNAPSHOT),
    COUNTRIES_SOURCE: ExportTarget(COUNTRIES_TARGET, ExportType.SNAPSHOT),
    FULL_ORDER_ITEM_REFUNDS_SOURCE: ExportTarget(FULL_ORDER_ITEM_REFUNDS_TARGET, ExportType.SNAPSHOT),
    INSTANCE_ATTRIBUTE_VALUES_SOURCE: ExportTarget(INSTANCE_ATTRIBUTE_VALUES_TARGET, ExportType.SNAPSHOT),
    OFFER_PROPERTIES_SOURCE: ExportTarget(OFFER_PROPERTIES_TARGET, ExportType.SNAPSHOT),
    ORDER_FINANCIAL_REVENUE_SOURCE: ExportTarget(ORDER_FINANCIAL_REVENUE_TARGET, ExportType.SNAPSHOT),
    ORDER_ITEM_EXCHANGE_RATE_SOURCE: ExportTarget(ORDER_ITEM_EXCHANGE_RATE_TARGET, ExportType.SNAPSHOT),
    ORDER_ITEM_OFFERS_SOURCE: ExportTarget(ORDER_ITEM_OFFERS_TARGET, ExportType.SNAPSHOT),
    PRODUCT_ATTRIBUTE_VALUES_SOURCE: ExportTarget(PRODUCT_ATTRIBUTE_VALUES_TARGET, ExportType.SNAPSHOT),
    PRODUCT_CATEGORIES_SOURCE: ExportTarget(PRODUCT_CATEGORIES_TARGET, ExportType.SNAPSHOT),
    PRODUCT_RANKINGS_SOURCE: ExportTarget(PRODUCT_RANKINGS_TARGET, ExportType.SNAPSHOT),
}
