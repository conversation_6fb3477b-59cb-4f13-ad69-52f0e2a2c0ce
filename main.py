import time  # 🕒 time module for waiting time
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from google.protobuf import field_mask_pb2
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import gspread
from refbetl import get_secret
import json

# ✅ Google Sheets API Configuration
google_sheet_secret = json.loads(get_secret("performance_marketing_google_sheet"))
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]
CLIENT_ID = google_sheet_secret["client_id"]
CLIENT_SECRET = google_sheet_secret["client_secret"]
REFRESH_TOKEN_SHEETS = google_sheet_secret["refresh_token_sheets"]

# ✅ Google Ads API Configuration
CONFIG = json.loads(get_secret("performance_marketing_google_ads"))
CONFIG["use_proto_plus"] = True

# ✅ Spreadsheet-Details
SPREADSHEET_ID = "1JkOmHrHugtmkgZWur-N7IoODfmFf_ahTFXIk_z1-8n8"
TAB_NAME = "adjustment export"

# ✅ OAuth2 for Google Sheets
def get_google_sheets_client():
    creds = Credentials(
        None,
        refresh_token=REFRESH_TOKEN_SHEETS,
        token_uri="https://oauth2.googleapis.com/token",
        client_id=CLIENT_ID,
        client_secret=CLIENT_SECRET,
        scopes=SCOPES,
    )

    if not creds.valid:
        creds.refresh(Request())

    return gspread.authorize(creds)

# ✅ Gets Seasonal Adjustments from Google Sheets (with 5 second delay)
def fetch_seasonal_adjustments():
    client = get_google_sheets_client()
    sheet = client.open_by_key(SPREADSHEET_ID).worksheet(TAB_NAME)

    print("⏳ Wait 5 seconds before loading data...")
    time.sleep(5)  # **⏳ Wait 5 seconds!**

    # Load all lines except the header line
    data = sheet.get_all_values()[1:]

    adjustments = []
    for row in data:
        try:
            adjustment = {
                "name": row[0],  # Adjustment Name
                "start_time": row[1],  # Start Time (YYYY-MM-DD HH:MM:SS)
                "end_time": row[2],  # End Time (YYYY-MM-DD HH:MM:SS)
                "adjustment_value": float(row[3].replace(",", ".")),  # New Value (comma to period)
                "customer_id": row[4],  # Customer Account ID
            }
            adjustments.append(adjustment)
        except Exception as e:
            print(f"⚠ Error processing a row: {row} -> {e}")

    return adjustments

# ✅ Search for Seasonal Adjustment in Google Ads
def find_seasonal_adjustment(client, customer_id, adjustment_name):
    google_ads_service = client.get_service("GoogleAdsService")

    query = f"""
        SELECT
            bidding_seasonality_adjustment.resource_name,
            bidding_seasonality_adjustment.name,
            bidding_seasonality_adjustment.conversion_rate_modifier,
            bidding_seasonality_adjustment.start_date_time,
            bidding_seasonality_adjustment.end_date_time
        FROM bidding_seasonality_adjustment
        WHERE bidding_seasonality_adjustment.name = '{adjustment_name}'
        LIMIT 1
    """

    try:
        response = google_ads_service.search(customer_id=customer_id, query=query)
        for row in response:
            adjustment = row.bidding_seasonality_adjustment
            print(f"📌 Found: {adjustment.name} ➝ {adjustment.resource_name}")
            return adjustment.resource_name
    except GoogleAdsException as ex:
        print(f"❌ Error retrieving: {ex}")
    return None

# ✅ Updated Seasonal Adjustment in Google Ads (including start/end time)
def update_seasonal_adjustment(client, customer_id, resource_name, new_value, start_time, end_time):
    adjustment_service = client.get_service("BiddingSeasonalityAdjustmentService")
    adjustment_operation = client.get_type("BiddingSeasonalityAdjustmentOperation")

    adjustment = adjustment_operation.update
    adjustment.resource_name = resource_name
    adjustment.conversion_rate_modifier = new_value
    adjustment.start_date_time = start_time
    adjustment.end_date_time = end_time

    # ✅ FieldMask for ALL fields to be updated
    adjustment_operation.update_mask.CopyFrom(
        field_mask_pb2.FieldMask(
            paths=["conversion_rate_modifier", "start_date_time", "end_date_time"]
        )
    )

    try:
        response = adjustment_service.mutate_bidding_seasonality_adjustments(
            customer_id=customer_id, operations=[adjustment_operation]
        )
        print(f"✅ Updated successfully: {response.results[0].resource_name}")
    except GoogleAdsException as ex:
        print(f"❌ Error updating: {ex}")

# ✅ Main function: Loads data & updates Google Ads
def run(request) -> str:
    """Reads Seasonal Adjustments from Google Sheets and updates Google Ads."""
    client = GoogleAdsClient.load_from_dict(CONFIG)

    print("📊 Download Seasonal Adjustments from Google Sheets...")
    adjustments = fetch_seasonal_adjustments()

    if adjustments:
        print(f"✅ {len(adjustments)} Seasonal Adjustments found. Starting Update...")
        for adj in adjustments:
            print(f"🔍 Searching for {adj['name']} for Account {adj['customer_id']}...")

            resource_name = find_seasonal_adjustment(client, adj["customer_id"], adj["name"])
            if resource_name:
                print(f"🔄 Update {adj['name']} to {int(round((adj['adjustment_value'] - 1) * 100))}%...")
                update_seasonal_adjustment(
                    client,
                    adj["customer_id"],
                    resource_name,
                    adj["adjustment_value"],
                    adj["start_time"],
                    adj["end_time"]
                )
            else:
                print(f"❌ Seasonal Adjustment '{adj['name']}' not found!")
    else:
        print("⚠ No data found!")

    return "Done"


if __name__ == "__main__":
    """Run with no fear locally as it only gets info and prints, DOESN'T UPDATE"""
    client = GoogleAdsClient.load_from_dict(CONFIG)

    print("📊 Download Seasonal Adjustments from Google Sheets...")
    adjustments = fetch_seasonal_adjustments()

    if adjustments:
        print(f"✅ {len(adjustments)} Seasonal Adjustments found. Starting Update...")
        for adj in adjustments:
            print(f"🔍 Searching for {adj['name']} for Account {adj['customer_id']}...")

            resource_name = find_seasonal_adjustment(client, adj["customer_id"], adj["name"])
            if resource_name:
                print(f"🔄 WOULD Update {adj['name']} to {int(round((adj['adjustment_value'] - 1) * 100))}%...")
            else:
                print(f"❌ Seasonal Adjustment '{adj['name']}' not found!")
    else:
        print("⚠ No data found!")
