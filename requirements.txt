# GCP clients
functions_framework==3.9.2 # includes correct version of cloudevents
google==3.0.0
google-cloud-secret-manager==2.24.0
google-cloud-parametermanager==0.1.5
google-cloud-storage==3.2.0
google-cloud-pubsub==2.31.1
google-cloud-bigquery==3.35.1
google-cloud-bigquery-storage==2.32.0
google-cloud-logging==3.12.1
google-cloud-dataproc==5.21.0

# cloud-sql
cloud-sql-python-connector[pg8000]==1.18.3
pg8000==1.31.4
sqlalchemy==2.0.42

# gcp admin api
google-api-python-client==2.177.0
google-api-python-client-stubs==1.30.0

# dataframe libs
pyarrow==21.0.0
pandas[pyarrow]==2.3.1
pandas-gbq==0.29.2
duckdb==1.3.2
db-dtypes==1.4.3

# Apache Spark
# must be compatible with dataproc serverless 2.2 runtime:
# https://cloud.google.com/dataproc-serverless/docs/concepts/versions/dataproc-serverless-versions
pyspark==3.5.1

# AWS
botocore==1.40.3
boto3==1.40.3
boto3-stubs[essential]==1.40.3
boto3-type-annotations==0.3.1

# notifications
pytz==2025.2
tabulate2==1.10.2

# other dependencies
dataclasses-json==0.6.7
paramiko==4.0.0
requests==2.32.4
lxml==6.0.0
thefuzz==0.22.1
beautifulsoup4==4.13.4

psutil==7.0.0
jsonschema[format]==4.25.0
sh==2.2.2
tenacity==9.1.2
pycron==3.2.0
pyyaml==6.0.2

# Google clients
gspread==6.2.1
google-ads==28.0.0

# requests testing
pytest-httpserver==1.1.3
