fail_fast: true

repos:
  # Some out-of-the-box hooks for pre-commit
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-added-large-files
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.37.1
    hooks:
      - id: yamllint
        name: yaml
        args: ['-d {extends: relaxed, rules: {line-length: {max: 120}}}', '-s']


  # The Uncompromising Code Formatter
  - repo: https://github.com/ambv/black
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3.12

  # Sorts all your imports
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        language_version: python3.12
        name: isort (python)

  # Static type checker for Python
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.1
    hooks:
      - id: mypy
        args: ['--ignore-missing-imports', '--enable-incomplete-feature=NewGenericSyntax']
        additional_dependencies:
          - types-requests
          - types-paramiko
          - types-protobuf
          - types-pytz
          - types-tabulate
          - types-PyYAML

  # Python Style Guide Enforcement
  - repo: https://github.com/PyCQA/flake8
    rev: 7.3.0
    hooks:
      - id: flake8
        # https://github.com/PyCQA/pycodestyle/issues/373
        args: ['--ignore=E203']

  # SQL Linter for postgres scripts
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 3.4.2
    hooks:
      - id: sqlfluff-fix
        name: sqlfluff-fix-postgres
        files: ^src/sql-pg/|^src/analytics_db_refresh/sql/
        args: ['--dialect', 'postgres', '--config', './src/sql-pg/.sqlfluff']
        additional_dependencies: []
      - id: sqlfluff-lint
        name: sqlfluff-lint-postgres
        files: ^src/sql-pg/|^src/analytics_db_refresh/sql/
        args: ['--dialect', 'postgres', '--config', './src/sql-pg/.sqlfluff']
        additional_dependencies: []

  # SQL Linter for BigQuery scripts
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 3.4.2
    hooks:
      - id: sqlfluff-fix
        name: sqlfluff-fix-bigquery
        files: ^src/sql-bq/
        args: ['--dialect', 'bigquery']
        additional_dependencies: []
      - id: sqlfluff-lint
        name: sqlfluff-lint-bigquery
        files: ^src/sql-bq/
        args: ['--dialect', 'bigquery']
        additional_dependencies: []

  # SQL Linter for DuckDb scripts
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 3.4.2
    hooks:
      - id: sqlfluff-fix
        name: sqlfluff-fix-duckdb
        files: ^src/pipedrive/pipedrive_transformer/
        args: ['--dialect', 'duckdb']
        additional_dependencies: []
      - id: sqlfluff-lint
        name: sqlfluff-lint-uckdb
        files: ^src/pipedrive/pipedrive_transformer/
        args: ['--dialect', 'duckdb']
        additional_dependencies: []


  # Bash shellcheck
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck
        name: bash-shellcheck

  # Terraform
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.99.5
    hooks:
      - id: terraform_fmt
        name: terraform-fmt
        args:
          - --args=-recursive
          - --hook-config=--parallelism-ci-cpu-cores=4
