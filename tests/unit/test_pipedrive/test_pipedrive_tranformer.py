from pipedrive_model import (
    Closed<PERSON>ate,
    DealExtraField,
    EnteredDate,
    LiveSelling,
    Manager,
    Merchant,
    OrganizationCustomField,
    OrganizationExtraField,
    OrganizationLabel,
    PersonExtraField,
    PersonRole,
)
from pipedrive_transformer.pipedrive_transformer import PipedriveTransformer

from common.time_service import TimeService


def test_set_organization_custom_fields(
    organization_custom_fields: list[OrganizationCustomField],
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = organization_custom_fields

    # act
    pipedrive_transformer.set_extracted().set_organization_extra_fields().set_organization_custom_fields()
    actual = pipedrive_transformer.get_table_entities(OrganizationCustomField)

    # assert
    assert actual == expected


def test_set_organization_extra_fields(
    organization_extra_fields: list[OrganizationExtraField],
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = organization_extra_fields

    # act
    pipedrive_transformer.set_extracted().set_organization_extra_fields()
    actual = pipedrive_transformer.get_table_entities(OrganizationExtraField)

    # assert
    assert actual == expected


def test_set_organization_labels(
    organization_labels: list[OrganizationLabel],
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = organization_labels

    # act
    pipedrive_transformer.set_extracted().set_organization_labels()
    actual = pipedrive_transformer.get_table_entities(OrganizationLabel)

    # assert
    assert actual == expected


def test_set_person_extra_fields(
    person_extra_fields: list[PersonExtraField],
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = person_extra_fields

    # act
    pipedrive_transformer.set_extracted().set_person_extra_fields()
    actual = pipedrive_transformer.get_table_entities(PersonExtraField)

    # assert
    assert actual == expected


def test_set_deal_extra_fields(
    deal_extra_fields: list[DealExtraField],
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = deal_extra_fields

    # act
    pipedrive_transformer.set_extracted().set_deal_extra_fields()
    actual = pipedrive_transformer.get_table_entities(DealExtraField)

    # assert
    assert actual == expected


def test_set_managers(
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = [Manager(org_id=1, account_manager="Max Mustermann", new_sales_manager="Jan Kowalski")]

    # act
    pipedrive_transformer.set_extracted().set_organization_extra_fields().set_managers()
    actual = pipedrive_transformer.get_table_entities(Manager)

    # assert
    assert actual == expected


def test_set_merchants(
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = [Merchant(org_id=1, merchant_id=123, merchant_name="John Doe Stuff")]

    # act
    pipedrive_transformer.set_extracted().set_organization_extra_fields().set_merchants()
    actual = pipedrive_transformer.get_table_entities(Merchant)

    # assert
    assert actual == expected


def test_set_entered_dates(
    pipedrive_transformer: PipedriveTransformer,
    time_service: TimeService,
) -> None:
    # arrange
    expected = [EnteredDate(org_id=1, person_id=1, date_entered=time_service.now.date())]

    # act
    pipedrive_transformer.set_extracted().set_deal_extra_fields().set_entered_dates()
    actual = pipedrive_transformer.get_table_entities(EnteredDate)

    # assert
    assert actual == expected


def test_set_closed_dates(
    pipedrive_transformer: PipedriveTransformer,
    time_service: TimeService,
) -> None:
    # arrange
    expected = [ClosedDate(org_id=1, date_closed=time_service.now.date())]

    # act
    pipedrive_transformer.set_extracted().set_organization_extra_fields().set_closed_dates()
    actual = pipedrive_transformer.get_table_entities(ClosedDate)

    # assert
    assert actual == expected


def test_set_live_selling(
    pipedrive_transformer: PipedriveTransformer,
    time_service: TimeService,
) -> None:
    # arrange
    expected = [LiveSelling(org_id=1, person_id=1, live_selling=time_service.now)]

    # act
    pipedrive_transformer.set_extracted().set_live_selling()
    actual = pipedrive_transformer.get_table_entities(LiveSelling)

    # assert
    assert actual == expected


def test_set_person_roles(
    pipedrive_transformer: PipedriveTransformer,
) -> None:
    # arrange
    expected = [
        PersonRole(person_id=1, contact_roles=["General", "Performance"], has_performance_role=True),
        PersonRole(person_id=2, contact_roles=["General"], has_performance_role=False),
    ]

    # act
    pipedrive_transformer.set_extracted().set_person_extra_fields().set_person_roles()
    actual = pipedrive_transformer.get_table_entities(PersonRole)

    # assert
    assert actual == expected
