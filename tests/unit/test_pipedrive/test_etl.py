from unittest.mock import Mock, patch

from pipedrive_model import PipedriveConfig, PipedriveExtracted, PipedriveTransformed

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.time_service import TimeService
from pipedrive.etl import PipedriveEtl


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_extract(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    pipedrive_extracted: PipedriveExtracted,
    pipedrive_etl: PipedriveEtl,
    pipedrive_config: PipedriveConfig,
) -> None:
    # act
    actual = pipedrive_etl.extract()

    # assert
    assert actual == pipedrive_extracted
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    pipedrive_extracted: PipedriveExtracted,
    pipedrive_etl: PipedriveEtl,
    pipedrive_config: PipedriveConfig,
    time_service: TimeService,
) -> None:
    # arrange
    expected = [
        PipedriveTransformed(
            organization_id=1,
            organization_labels=["foo", "bar", "baz"],
            new_sales_manager="Jan Kowalski",
            new_sales_manager_email="<EMAIL>",
            merchant_id=123,
            merchant_name="John Doe Stuff",
            primary_email="<EMAIL>",
            all_emails=[
                {"label": "work", "main": "<EMAIL>", "primary": "true"},
                {"label": "help", "main": "<EMAIL>", "primary": "false"},
            ],
            account_manager="Max Mustermann",
            account_manager_email="<EMAIL>",
            account_name="John Doe Org",
            first_name="John",
            last_name="Doe",
            contact_roles=["General", "Performance"],
            has_performance_role=True,
            live_selling=time_service.now,
            date_entered=time_service.now.date(),
            date_closed=time_service.now.date(),
            probability_of_default=0.1235,
            vat_number="AT123456789",
            credit_score_on_monitor="Yes",
            credit_score_when_live="Green",
            onboarding_reserve_plan_agreed="Yes",
            finance_verification_date=time_service.today,
            sepa_status="not handed in",
            collection_status="Insolvent",
            supplier_type=["Reseller", "Refurbisher Level 2"],
        ),
        PipedriveTransformed(
            organization_id=1,
            organization_labels=["foo", "bar", "baz"],
            new_sales_manager="Jan Kowalski",
            new_sales_manager_email="<EMAIL>",
            merchant_id=123,
            merchant_name="John Doe Stuff",
            primary_email="<EMAIL>",
            all_emails=[
                {"label": "work", "main": "<EMAIL>", "primary": "true"},
                {"label": "help", "main": "<EMAIL>", "primary": "false"},
            ],
            account_manager="Max Mustermann",
            account_manager_email="<EMAIL>",
            account_name="John Doe Org",
            first_name="Mr.",
            last_name="Bean",
            contact_roles=["General"],
            has_performance_role=False,
            live_selling=None,
            date_entered=None,
            date_closed=time_service.now.date(),
            probability_of_default=0.1235,
            vat_number="AT123456789",
            credit_score_on_monitor="Yes",
            credit_score_when_live="Green",
            onboarding_reserve_plan_agreed="Yes",
            finance_verification_date=time_service.today,
            sepa_status="not handed in",
            collection_status="Insolvent",
            supplier_type=["Reseller", "Refurbisher Level 2"],
        ),
    ]

    # act: dump
    given_paths = pipedrive_etl.dump(pipedrive_extracted)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()

    # act: transform
    mock_start_processing_step.reset_mock()
    mock_finish_processing_step.reset_mock()
    actual = pipedrive_etl.transform(given_paths)

    # assert
    assert actual == expected
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
