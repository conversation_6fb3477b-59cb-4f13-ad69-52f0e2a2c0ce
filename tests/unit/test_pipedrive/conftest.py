from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from pipedrive_api_client import PipedriveApiClient, PipedriveApiConfig
from pipedrive_bq_repo import PipedriveBqRepository
from pipedrive_model import (
    DealExtraField,
    KeyVal,
    OrganizationCustomField,
    OrganizationExtraField,
    OrganizationLabel,
    PersonExtraField,
    PipedriveConfig,
    PipedriveDeal,
    PipedriveExtracted,
    PipedriveField,
    PipedriveOrganization,
    <PERSON><PERSON><PERSON><PERSON>erson,
    Pipedrive<PERSON><PERSON>,
)
from pipedrive_pg_repo import PipedrivePgRepository
from pipedrive_transformer import PipedriveTransformer

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.time_service import TimeService
from pipedrive.etl import PipedriveEtl


@pytest.fixture(scope="session")
def pipedrive_config() -> PipedriveConfig:
    return PipedriveConfig(api_secret="secret")


@pytest.fixture(scope="session")
def pipedrive_api_config() -> PipedriveApiConfig:
    return PipedriveApiConfig(api_token="secret-token")


@pytest.fixture(scope="session")
def organization_extra_fields(time_service: TimeService) -> list[OrganizationExtraField]:
    date = time_service.now.date().isoformat()
    return [
        OrganizationExtraField(field_key="#account_manger_uuid", field_value="1", org_id=1),
        OrganizationExtraField(field_key="#new_sales_manger_uuid", field_value="2", org_id=1),
        OrganizationExtraField(field_key="#date_closed_uuid", field_value=date, org_id=1),
        OrganizationExtraField(field_key="#mechant_id_uuid", field_value="123", org_id=1),
        OrganizationExtraField(field_key="#mechant_name_uuid", field_value="John Doe Stuff", org_id=1),
        OrganizationExtraField(field_key="#probability_of_default", field_value="0.*********", org_id=1),
        OrganizationExtraField(field_key="#vat_number", field_value="AT*********", org_id=1),
        OrganizationExtraField(field_key="#credit_score_on_monitor", field_value="1", org_id=1),
        OrganizationExtraField(field_key="#credit_score_when_live", field_value="2", org_id=1),
        OrganizationExtraField(field_key="#onboarding_reserve_plan_agreed", field_value="3", org_id=1),
        OrganizationExtraField(field_key="#sepa_status", field_value="4", org_id=1),
        OrganizationExtraField(field_key="#collection_status", field_value="5", org_id=1),
        OrganizationExtraField(field_key="#supplier_type", field_value="6,7", org_id=1),
        OrganizationExtraField(
            field_key="#finance_verification_date", field_value=time_service.today.isoformat(), org_id=1
        ),
    ]


@pytest.fixture(scope="session")
def organization_custom_fields(time_service: TimeService) -> list[OrganizationCustomField]:
    return [
        OrganizationCustomField(
            org_id=1,
            probability_of_default=0.1235,
            vat_number="AT*********",
            credit_score_on_monitor="Yes",
            credit_score_when_live="Green",
            onboarding_reserve_plan_agreed="Yes",
            finance_verification_date=time_service.today,
            sepa_status="not handed in",
            collection_status="Insolvent",
            supplier_type=["Reseller", "Refurbisher Level 2"],
        ),
    ]


@pytest.fixture(scope="session")
def person_extra_field_1() -> PersonExtraField:
    return PersonExtraField(
        field_key="#contact_role_uuid", field_value="#general_role_id, #perf_role_id", org_id=1, person_id=1
    )


@pytest.fixture(scope="session")
def person_extra_field_2() -> PersonExtraField:
    return PersonExtraField(field_key="#contact_role_uuid", field_value="#general_role_id", org_id=1, person_id=2)


@pytest.fixture(scope="session")
def person_extra_fields(
    person_extra_field_1: PersonExtraField, person_extra_field_2: PersonExtraField
) -> list[PersonExtraField]:
    return [person_extra_field_1, person_extra_field_2]


@pytest.fixture(scope="session")
def pipedrive_users() -> list[PipedriveUser]:
    return [
        PipedriveUser(id=1, name="Max Mustermann", email="<EMAIL>"),
        PipedriveUser(id=2, name="Jan Kowalski", email="<EMAIL>"),
    ]


@pytest.fixture(scope="session")
def pipedrive_organizations(
    organization_extra_fields: list[OrganizationExtraField], time_service: TimeService
) -> list[PipedriveOrganization]:
    return [
        PipedriveOrganization(
            id=1,
            company_id=1,
            name="John Doe Org",
            open_deals_count=1,
            related_open_deals_count=1,
            closed_deals_count=1,
            related_closed_deals_count=1,
            email_messages_count=1,
            people_count=1,
            activities_count=1,
            done_activities_count=1,
            undone_activities_count=1,
            files_count=1,
            notes_count=1,
            followers_count=1,
            won_deals_count=1,
            related_won_deals_count=1,
            lost_deals_count=1,
            related_lost_deals_count=1,
            active_flag=True,
            country_code="AT",
            update_time=time_service.now,
            add_time=time_service.now,
            address="Some address",
            owner_name="John Doe",
            cc_email="<EMAIL>",
            extra_fields=[KeyVal.from_dict(oef.to_dict()).to_dict() for oef in organization_extra_fields],
            label_ids=[1, 2, 3],
        )
    ]


@pytest.fixture(scope="session")
def pipedrive_organization_fields(time_service: TimeService) -> list[PipedriveField]:
    return [
        PipedriveField(
            key="#account_manger_uuid",
            id=1,
            name="Account Manager",
            field_type="varchar",
            options=[
                {"id": "1", "label": "Max Mustermann"},
            ],
            org_id=1,
        ),
        PipedriveField(
            key="#date_closed_uuid",
            id=1,
            name="seller guide signed",
            field_type="date",
            options=[{"id": "1", "label": "foo"}, {"id": "2", "label": "bar"}],
            org_id=1,
        ),
        PipedriveField(
            key="#new_sales_manger_uuid",
            id=1,
            name="New Sales Manager",
            field_type="varchar",
            options=[
                {"id": "2", "label": "Jan Kowalski"},
            ],
            org_id=1,
        ),
        PipedriveField(
            key="#mechant_id_uuid",
            id=1,
            name="Merchant ID",
            field_type="varchar",
            org_id=1,
        ),
        PipedriveField(
            key="#mechant_name_uuid",
            id=1,
            name="refurbed display name",
            field_type="varchar",
            org_id=1,
        ),
        PipedriveField(
            key="#probability_of_default",
            id=1,
            name="Probability Of Default",
            field_type="double",
            org_id=1,
            field_value="0.1",
        ),
        PipedriveField(
            key="#vat_number",
            id=1,
            name="VAT number",
            field_type="varchar",
            field_value="AT*********",
        ),
        PipedriveField(
            key="#credit_score_on_monitor",
            id=1,
            name="Credit Score on Monitor",
            field_type="enum",
            options=[{"id": "1", "label": "Yes"}],
            org_id=1,
        ),
        PipedriveField(
            key="#credit_score_when_live",
            id=1,
            name="Credit Score when Live",
            field_type="enum",
            options=[{"id": "2", "label": "Green"}],
            org_id=1,
        ),
        PipedriveField(
            key="#onboarding_reserve_plan_agreed",
            id=1,
            name="Onboarding Reserve Plan Agreed",
            field_type="enum",
            options=[{"id": "3", "label": "Yes"}],
            org_id=1,
        ),
        PipedriveField(
            key="#sepa_status",
            id=1,
            name="SEPA Status",
            field_type="enum",
            options=[{"id": "4", "label": "not handed in"}],
            org_id=1,
        ),
        PipedriveField(
            key="#collection_status",
            id=1,
            name="Collection Status",
            field_type="enum",
            options=[{"id": "5", "label": "Insolvent"}],
            org_id=1,
        ),
        PipedriveField(
            key="#supplier_type",
            id=1,
            name="Supplier Type",
            field_type="set",
            options=[{"id": "6", "label": "Reseller"}, {"id": "7", "label": "Refurbisher Level 2"}],
            org_id=1,
        ),
        PipedriveField(
            key="#finance_verification_date",
            id=1,
            name="Finance verification date",
            field_type="date",
            field_value=time_service.today.isoformat(),
            org_id=1,
        ),
        PipedriveField(
            key="#labels",
            id=1,
            name="Labels",
            field_type="set",
            options=[
                {"id": 1, "label": "foo"},
                {"id": 2, "label": "bar"},
                {"id": 3, "label": "baz"},
                {"id": 4, "label": "qux"},
            ],
            org_id=1,
        ),
    ]


@pytest.fixture(scope="session")
def organization_labels() -> list[OrganizationLabel]:
    return [OrganizationLabel(org_id=1, labels=["foo", "bar", "baz"])]


@pytest.fixture(scope="session")
def pipedrive_persons(
    time_service: TimeService,
    person_extra_field_1: PersonExtraField,
    person_extra_field_2: PersonExtraField,
) -> list[PipedrivePerson]:
    return [
        PipedrivePerson(
            id=1,
            company_id=1,
            org_id={"value": 1},
            name="John Doe",
            first_name="John",
            last_name="Doe",
            open_deals_count=1,
            related_open_deals_count=1,
            closed_deals_count=1,
            related_closed_deals_count=1,
            participant_open_deals_count=1,
            participant_closed_deals_count=1,
            email_messages_count=1,
            activities_count=1,
            done_activities_count=1,
            undone_activities_count=1,
            files_count=1,
            notes_count=1,
            followers_count=1,
            won_deals_count=1,
            related_won_deals_count=1,
            lost_deals_count=1,
            related_lost_deals_count=1,
            active_flag=True,
            phone=[{"label": "work", "value": "+48*********", "primary": "true"}],
            email=[
                {"label": "work", "main": "<EMAIL>", "primary": "true"},
                {"label": "help", "main": "<EMAIL>", "primary": "false"},
            ],
            update_time=time_service.now,
            delete_time=None,
            add_time=time_service.now,
            next_activity_date=None,
            last_activity_date=time_service.today,
            postal_address="Some address",
            postal_address_lat=None,
            postal_address_long=None,
            postal_address_postal_code="123456",
            birthday="Some string date",
            job_title="manager",
            org_name="Best iphones",
            primary_email="<EMAIL>",
            cc_email="<EMAIL>",
            extra_fields=[KeyVal.from_dict(person_extra_field_1.to_dict()).to_dict()],
        ),
        PipedrivePerson(
            id=2,
            company_id=1,
            org_id={"value": 1},
            name="Mr. Bean",
            first_name="Mr.",
            last_name="Bean",
            open_deals_count=1,
            related_open_deals_count=1,
            closed_deals_count=1,
            related_closed_deals_count=1,
            participant_open_deals_count=1,
            participant_closed_deals_count=1,
            email_messages_count=1,
            activities_count=1,
            done_activities_count=1,
            undone_activities_count=1,
            files_count=1,
            notes_count=1,
            followers_count=1,
            won_deals_count=1,
            related_won_deals_count=1,
            lost_deals_count=1,
            related_lost_deals_count=1,
            active_flag=True,
            phone=[{"label": "work", "value": "999", "primary": "true"}],
            email=[
                {"label": "work", "main": "<EMAIL>", "primary": "true"},
                {"label": "help", "main": "<EMAIL>", "primary": "false"},
            ],
            update_time=time_service.now,
            delete_time=None,
            add_time=time_service.now,
            next_activity_date=None,
            last_activity_date=time_service.today,
            postal_address="Some address",
            postal_address_lat=None,
            postal_address_long=None,
            postal_address_postal_code="123456",
            birthday="Some string date",
            job_title="manager",
            org_name="Best iphones",
            primary_email="<EMAIL>",
            cc_email="<EMAIL>",
            extra_fields=[KeyVal.from_dict(person_extra_field_2.to_dict()).to_dict()],
        ),
    ]


@pytest.fixture(scope="session")
def pipedrive_person_fields(time_service: TimeService) -> list[PipedriveField]:
    return [
        PipedriveField(
            key="#contact_role_uuid",
            id=1,
            name="Contact Role",
            field_type="set",
            options=[{"id": "#general_role_id", "label": "General"}, {"id": "#perf_role_id", "label": "Performance"}],
            org_id=1,
        )
    ]


@pytest.fixture(scope="session")
def deal_extra_fields(time_service: TimeService) -> list[DealExtraField]:
    date = time_service.now.date().isoformat()
    return [
        DealExtraField(field_key="#date_entered_uuid", field_value=date, deal_id=1),
        DealExtraField(field_key="#date_closed_uuid", field_value=date, deal_id=1),
    ]


@pytest.fixture(scope="session")
def pipedrive_deals(deal_extra_fields: list[DealExtraField], time_service: TimeService) -> list[PipedriveDeal]:
    return [
        PipedriveDeal(
            id=1,
            org_id={"value": 1},
            person_id={"value": 1},
            stage_id=1,
            title="Best deal ever",
            value=99.99,
            currency="EUR",
            add_time=time_service.now,
            update_time=time_service.now,
            active=True,
            deleted=False,
            status="test",
            pipeline_id=1,
            won_time=time_service.now,
            first_won_time=time_service.now,
            products_count=1,
            activities_count=1,
            extra_fields=[KeyVal.from_dict(deal.to_dict()).to_dict() for deal in deal_extra_fields],
        )
    ]


@pytest.fixture(scope="session")
def pipedrive_deal_fields(time_service: TimeService) -> list[PipedriveField]:
    return [
        PipedriveField(
            key="#date_entered_uuid",
            id=1,
            name="date entered",
            field_type="some type",
            org_id=1,
        ),
        PipedriveField(
            key="#date_closed_uuid",
            id=1,
            name="seller guide signed",
            field_type="some type",
            org_id=1,
        ),
    ]


@pytest.fixture(scope="session")
def pipedrive_extracted(
    pipedrive_users: list[PipedriveUser],
    pipedrive_organizations: list[PipedriveOrganization],
    pipedrive_organization_fields: list[PipedriveField],
    pipedrive_persons: list[PipedrivePerson],
    pipedrive_person_fields: list[PipedriveField],
    pipedrive_deals: list[PipedriveDeal],
    pipedrive_deal_fields: list[PipedriveField],
) -> PipedriveExtracted:
    return PipedriveExtracted(
        users=pipedrive_users,
        organizations=pipedrive_organizations,
        organization_fields=pipedrive_organization_fields,
        persons=pipedrive_persons,
        person_fields=pipedrive_person_fields,
        deals=pipedrive_deals,
        deal_fields=pipedrive_deal_fields,
    )


@pytest.fixture(scope="session")
def pipedrive_api_client_mock(
    pipedrive_users: list[PipedriveUser],
    pipedrive_organizations: list[PipedriveOrganization],
    pipedrive_organization_fields: list[PipedriveField],
    pipedrive_persons: list[PipedrivePerson],
    pipedrive_person_fields: list[PipedriveField],
    pipedrive_deals: list[PipedriveDeal],
    pipedrive_deal_fields: list[PipedriveField],
) -> PipedriveApiClient:
    mock = Mock(spec_set=PipedriveApiClient)

    mock.get_users.return_value = pipedrive_users
    mock.get_organisations.return_value = pipedrive_organizations
    mock.get_organisation_fields.return_value = pipedrive_organization_fields
    mock.get_persons.return_value = pipedrive_persons
    mock.get_person_fields.return_value = pipedrive_person_fields
    mock.get_deals.return_value = pipedrive_deals
    mock.get_deal_fields.return_value = pipedrive_deal_fields

    return mock


@pytest.fixture(scope="session")
def pipedrive_api_client_fake(pipedrive_api_config: PipedriveApiConfig) -> PipedriveApiClient:
    return PipedriveApiClient(config=pipedrive_api_config)


@pytest.fixture(scope="function")
def pipedrive_bq_repository_mock() -> Mock:
    return Mock(spec=PipedriveBqRepository)


@pytest.fixture(scope="function")
def pipedrive_pg_repository_mock() -> Mock:
    return Mock(spec_set=PipedrivePgRepository)


@pytest.fixture(scope="function")
def pipedrive_etl(
    pipedrive_config: PipedriveConfig,
    pipedrive_api_client_mock: Mock,
    data_lake_repository_mock: Mock,
    pipedrive_bq_repository_mock: Mock,
    pipedrive_pg_repository_mock: Mock,
    tmp_path: Path,
) -> PipedriveEtl:
    return PipedriveEtl(
        config=pipedrive_config,
        api_client=pipedrive_api_client_mock,
        transformer_creator=PipedriveTransformer.create,
        local_temp_path=tmp_path,
        raw_data_lake_repository=data_lake_repository_mock,
        bq_repository=pipedrive_bq_repository_mock,
        pg_repository=pipedrive_pg_repository_mock,
    )


@pytest.fixture(scope="function")
@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def pipedrive_transformer(
    finish_processing_step: Mock,
    start_processing_step: Mock,
    pipedrive_extracted: PipedriveExtracted,
    pipedrive_etl: PipedriveEtl,
) -> PipedriveTransformer:
    paths = pipedrive_etl.dump(pipedrive_extracted)
    return PipedriveTransformer.create(paths)
