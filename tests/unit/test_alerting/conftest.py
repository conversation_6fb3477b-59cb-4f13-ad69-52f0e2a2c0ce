from datetime import UTC, datetime
from typing import Optional
from unittest.mock import Mock

import pytest
from alerting_config import DataEng<PERSON><PERSON><PERSON>abe<PERSON>
from alerting_shared.notifications import NotificationMessage
from incident_model import (
    Alert,
    CloudRunFunctionResource,
    CloudRunJobResource,
    CloudRunResourceType,
    Incident,
    Resource,
)

from alerting.incident_alerting import IncidentAlerting
from common.cloud_logging_client.client import Cloud<PERSON>ogging<PERSON><PERSON>
from common.cloud_run_client.function_client import CloudRunFunctionClient
from common.cloud_run_client.job_client import CloudRunJobClient
from common.cloud_run_client.model import CloudRunJobExecution, CloudRunResource
from common.cloud_run_client.service_client import CloudRunServiceClient
from common.config import MattermostAlertingConfig
from common.consts import (
    GCP_CONSOLE_BASE_URL,
    GCP_REGION,
    STAGING_GOOGLE_PROJECT_ID,
    AlertCriticality,
)
from common.time_service import TimeService

CLOUD_RUN_NAME_BASE = f"projects/{STAGING_GOOGLE_PROJECT_ID}/locations/{GCP_REGION}"
JOB_NAME = "alerting-job-check"
JOB_FULL_NAME = f"{CLOUD_RUN_NAME_BASE}/jobs/{JOB_NAME}"
FUNCTION_NAME = "alerting-function-check"
FUNCTION_FULL_NAME = f"{CLOUD_RUN_NAME_BASE}/functions/{FUNCTION_NAME}"
SERVICE_FULL_NAME = f"{CLOUD_RUN_NAME_BASE}/services/{FUNCTION_NAME}"


@pytest.fixture(scope="session")
def cloud_logging_client_mock() -> Mock:
    return Mock(spec=CloudLoggingClient)


@pytest.fixture(scope="session")
def cloud_run_job_client_mock() -> Mock:
    return Mock(spec=CloudRunJobClient)


@pytest.fixture(scope="session")
def cloud_function_client_mock() -> Mock:
    return Mock(spec=CloudRunFunctionClient)


@pytest.fixture(scope="session")
def cloud_run_service_client_mock() -> Mock:
    return Mock(spec=CloudRunServiceClient)


@pytest.fixture(scope="session")
def alerting_config() -> MattermostAlertingConfig:
    return MattermostAlertingConfig(webhook_url="test")


@pytest.fixture(scope="function")
def incident_alerting(
    alerting_config: MattermostAlertingConfig,
    mattermost_client_mock: Mock,
    cloud_run_job_client_mock: Mock,
    cloud_function_client_mock: Mock,
    cloud_run_service_client_mock: Mock,
    cloud_logging_client_mock: Mock,
) -> IncidentAlerting:
    return IncidentAlerting(
        config=alerting_config,
        mattermost_client=mattermost_client_mock,
        cloud_run_job_client=cloud_run_job_client_mock,
        cloud_function_client=cloud_function_client_mock,
        cloud_run_service_client=cloud_run_service_client_mock,
        cloud_logging_client=cloud_logging_client_mock,
    )


def get_job_alert() -> Alert:
    return Alert(
        incident=Incident(
            incident_id="job-incident-123",
            severity="Warning",
            state="closed",
            url=GCP_CONSOLE_BASE_URL,
            started_at=1730192550,
            resource=Resource(
                labels=CloudRunJobResource(job_name=JOB_NAME, location=GCP_REGION, project_id="test-project"),
                type=CloudRunResourceType.JOB,
            ),
        )
    )


@pytest.fixture(scope="session")
def job_alert() -> Alert:
    return get_job_alert()


def get_function_alert() -> Alert:
    return Alert(
        incident=Incident(
            incident_id="function-incident-123",
            severity="Error",
            state="closed",
            url=GCP_CONSOLE_BASE_URL,
            started_at=1730192550,
            resource=Resource(
                labels=CloudRunFunctionResource(
                    service_name=FUNCTION_NAME,
                    location=GCP_REGION,
                    project_id="test-project",
                ),
                type=CloudRunResourceType.REVISION,
            ),
        )
    )


def get_service_alert() -> Alert:
    return Alert(
        incident=Incident(
            incident_id="service-incident-123",
            severity="Warning",
            state="open",
            url=GCP_CONSOLE_BASE_URL,
            started_at=1749103281,
            resource=Resource(
                labels=CloudRunFunctionResource(
                    location=GCP_REGION, project_id="test-project", service_name=FUNCTION_NAME
                ),
                type=CloudRunResourceType.REVISION,
            ),
        )
    )


@pytest.fixture(scope="session")
def function_alert() -> Alert:
    return get_function_alert()


@pytest.fixture(scope="session")
def data_eng_labels() -> DataEngineeringLabels:
    owner = "data_engineering"
    return DataEngineeringLabels(owner=owner, backup=owner, criticality=AlertCriticality.P2, alerting_enabled=False)


@pytest.fixture(scope="session")
def cloud_run_job(time_service: TimeService, data_eng_labels: DataEngineeringLabels) -> CloudRunResource:
    return CloudRunResource(
        name=JOB_FULL_NAME,
        uid="123abc",
        create_time=time_service.now,
        update_time=time_service.now,
        labels=data_eng_labels.to_dict(),
    )


@pytest.fixture(scope="session")
def cloud_run_function(time_service: TimeService, data_eng_labels: DataEngineeringLabels) -> CloudRunResource:
    return CloudRunResource(
        name=FUNCTION_FULL_NAME,
        create_time=time_service.now,
        update_time=time_service.now,
        labels=data_eng_labels.to_dict(),
    )


@pytest.fixture(scope="session")
def cloud_run_job_execution(time_service: TimeService) -> CloudRunJobExecution:
    return CloudRunJobExecution(
        name=f"{JOB_NAME}-abc123",
        uid="foobar123",
        create_time=time_service.now,
        update_time=time_service.now,
        log_uri=GCP_CONSOLE_BASE_URL,
    )


def get_notification_message(
    alert: Alert,
    cloud_run_resource: CloudRunResource,
    data_eng_labels: DataEngineeringLabels,
    cloud_run_job_execution: Optional[CloudRunJobExecution] = None,
) -> NotificationMessage:
    return NotificationMessage(
        started_at=datetime(2024, 10, 29, 9, 2, 30, tzinfo=UTC),
        resource_name=alert.incident.resource.name,
        resource_url=cloud_run_resource.uri,
        incident_url=alert.incident.url,
        logs_url=cloud_run_job_execution.log_uri if cloud_run_job_execution else cloud_run_resource.log_uri,
        owner=data_eng_labels.owner,
        backup=data_eng_labels.backup,
        severity=alert.incident.severity.upper(),
        criticality=data_eng_labels.criticality,
        log_entries=[],
    )


@pytest.fixture(scope="session")
def job_notification_message(
    job_alert: Alert,
    cloud_run_job: CloudRunResource,
    cloud_run_job_execution: CloudRunJobExecution,
    data_eng_labels: DataEngineeringLabels,
) -> NotificationMessage:
    return get_notification_message(job_alert, cloud_run_job, data_eng_labels, cloud_run_job_execution)


@pytest.fixture(scope="session")
def function_notification_message(
    function_alert: Alert,
    cloud_run_function: CloudRunResource,
    data_eng_labels: DataEngineeringLabels,
) -> NotificationMessage:
    return get_notification_message(function_alert, cloud_run_function, data_eng_labels)
