import pytest
from refresh_shared.dump_restore import PgParams

from common.migrate_db import FlywayBqParams, FlywayPgParams


@pytest.mark.parametrize(
    "pg_params, command_params",
    [
        [
            PgParams(jobs=8, host="test-case-01"),
            [
                "--format",
                "directory",
                "--jobs",
                8,
                "--schema",
                "public",
                "--schema",
                "zendesk",
                "--clean",
                "--if-exists",
                "--no-privileges",
                "--no-owner",
                "--verbose",
            ],
        ],
        [
            PgParams(
                host="test-case-02",
                jobs=1,
                schema=["foo"],
                clean=False,
                if_exists=False,
                no_privileges=False,
                no_owner=False,
                verbose=False,
            ),
            ["--format", "directory", "--jobs", 1, "--schema", "foo"],
        ],
    ],
)
def test_pg_params(pg_params: PgParams, command_params: list[str]) -> None:
    # act & assert
    assert pg_params.command_params == command_params


@pytest.mark.parametrize(
    "flyway_params, command_params",
    [
        [
            FlywayPgParams(
                host="test-case-01",
                port=123,
                database="foo",
                config_files="/test/config",
                locations="sql/locations/",
                environment="test",
                user="bar",
                password="baz",
            ),
            "-configFiles=/test/config -locations=sql/locations/ -environment=test -user=bar '-password=baz' "
            "-url=**************************************",
        ],
        [
            FlywayPgParams(
                host="test-case-02",
                port=123,
                database="foo",
                config_files="/test/config",
                locations="sql/locations/",
                environment="test",
                user="bar",
                password="baz",
                placeholders=["foo=bar", "baz=bar", "baz=foo"],
            ),
            "-configFiles=/test/config -locations=sql/locations/ -environment=test -user=bar '-password=baz' "
            "-placeholders.foo=bar -placeholders.baz=bar -placeholders.baz=foo "
            "-url=**************************************",
        ],
    ],
)
def test_flyway_params(flyway_params: FlywayPgParams, command_params: str) -> None:
    # act & assert
    assert flyway_params.command_params == command_params


@pytest.mark.parametrize(
    "flyway_bq_params, expected_command_params",
    [
        [
            FlywayBqParams(
                project_id="test-project-123",
                config_files="/test/config",
                locations="sql/locations/",
                environment="test",
                user="test-user",
                password="test-password",
            ),
            "-configFiles=/test/config -locations=sql/locations/ -environment=test -user=test-user "
            "'-password=test-password' "
            "-url='*************************************************************************************************'",
        ],
        [
            FlywayBqParams(
                project_id="another-project",
                config_files="/another/config",
                locations="filesystem:/sql",
                environment="staging",
                placeholders=["runTest=true", "changeReason=20250101"],
            ),
            "-configFiles=/another/config -locations=filesystem:/sql -environment=staging "
            "-placeholders.runTest=true -placeholders.changeReason=20250101 "
            "-url='************************************************************************************************'",
        ],
    ],
)
def test_flyway_bq_params(flyway_bq_params: FlywayBqParams, expected_command_params: str) -> None:
    # act & assert
    assert flyway_bq_params.command_params == expected_command_params
    # Ensure project_id is not in command_params as a separate parameter
    assert "-projectId=" not in flyway_bq_params.command_params
