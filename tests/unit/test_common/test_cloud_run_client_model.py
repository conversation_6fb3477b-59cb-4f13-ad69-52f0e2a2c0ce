import re

import pytest
from conftest import TODAY, YESTERDAY

from common.cloud_run_client.model import JOB_BASE_URL, CloudRunResource
from common.consts import GCP_REGION, STAGING_GOOGLE_PROJECT_ID


def test_cloud_run_resource() -> None:
    # Arrange
    expected_short_name = "foo"
    name = f"projects/{STAGING_GOOGLE_PROJECT_ID}/locations/{GCP_REGION}/jobs/{expected_short_name}"
    given = CloudRunResource(name=name, create_time=YESTERDAY, update_time=TODAY, labels=dict())
    expected_uri = f"{JOB_BASE_URL}/{GCP_REGION}/{expected_short_name}?project_name={STAGING_GOOGLE_PROJECT_ID}"

    # Act
    actual_uri = given.uri

    # Assert
    assert actual_uri == expected_uri


def test_cloud_run_resource_uri_when_incorrect_name() -> None:
    # Arrange
    name = "foo"

    # Act & Assert
    with pytest.raises(
        ValueError,
        match=re.escape(
            "Resource name 'foo' does not match pattern "
            "'projects/(?P<project>.*)/locations/(?P<region>.*)/"
            "(?P<resource_type>jobs|functions|services)/(?P<short_name>.*)'!"
        ),
    ):
        _ = CloudRunResource(name=name, create_time=YESTERDAY, update_time=TODAY, labels=dict())
