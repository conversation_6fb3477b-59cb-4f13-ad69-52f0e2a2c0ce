import dataclasses
from datetime import timedelta
from unittest.mock import Mock

import pytest
from _pytest.logging import LogCapture<PERSON>ix<PERSON>
from black import datetime
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
    RestoreCloudSQLInstanceError,
)
from unit.test_analytics_db_refresh.test_refresh.utils import BACKUP_START_TIME

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    BackupType,
    CloudSQLCreateInstanceParams,
    CloudSQLInstance,
)


def test_recreate_instance(
    restore_instance: RestoreCloudSQLInstance,
    new_cloud_sql_instance: CloudSQLInstance,
    new_cloud_sql_instance_params: CloudSQLCreateInstanceParams,
    cloud_sql_instance_client_mock: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # Arrange
    caplog.set_level("WARNING")
    cloud_sql_instance_client_mock.describe_instance.return_value = new_cloud_sql_instance

    # Act
    restore_instance.recreate_instance()

    #  Assert
    cloud_sql_instance_client_mock.drop_instance.assert_called_once_with(
        name=new_cloud_sql_instance.name, wait_for_completion=True
    )
    cloud_sql_instance_client_mock.create_instance.assert_called_once_with(
        params=new_cloud_sql_instance_params, wait_for_completion=True
    )
    assert "Cloud SQL Instance 'test-instance' already exists, dropping the instance..." in caplog.messages


def test_try_get_today_backup(restore_instance: RestoreCloudSQLInstance, cloud_sql_backup_client_mock: Mock) -> None:
    # Arrange
    expected_backup = get_test_backup()
    cloud_sql_backup_client_mock.get_latest_backup.return_value = expected_backup

    # Act
    actual_backup = restore_instance.try_get_today_backup()

    # Assert
    assert actual_backup == expected_backup


def test_try_get_today_backup_should_wait_until_backup_is_not_ready(
    restore_instance: RestoreCloudSQLInstance, cloud_sql_backup_client_mock: Mock
) -> None:
    # Arrange
    expected_backup = get_test_backup()
    cloud_sql_backup_client_mock.get_latest_backup.side_effect = [
        # 1st iteration: No today's backup exists yet, get_latest_backup waits and loops again after a delay.
        get_test_backup(BACKUP_START_TIME - timedelta(days=1)),
        # 2nd iteration: Today's backup has been created, get_latest_backup can finish the iteration.
        get_test_backup(BACKUP_START_TIME),
    ]

    # Act
    actual_backup = restore_instance.try_get_today_backup(should_wait=True)

    # Assert
    assert actual_backup == expected_backup


def test_try_get_today_backup_when_should_wait_is_false_and_backup_not_ready(
    restore_instance: RestoreCloudSQLInstance, restore_start_time: datetime, cloud_sql_backup_client_mock: Mock
) -> None:
    # Arrange
    backup_date = restore_start_time - timedelta(days=1, hours=1)
    test_backup = get_test_backup(backup_date)
    expected_backup = dataclasses.replace(
        test_backup,
        start_time=restore_start_time - timedelta(days=1, hours=1),
        end_time=restore_start_time - timedelta(days=1),
    )
    cloud_sql_backup_client_mock.get_latest_backup.return_value = expected_backup

    # Act
    actual_backup = restore_instance.try_get_today_backup(should_wait=False)

    # Assert
    assert actual_backup == expected_backup


def test_try_get_today_backup_when_should_wait_and_no_backup(
    restore_instance: RestoreCloudSQLInstance,
    restore_start_time: datetime,
    cloud_sql_backup_client_mock: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # Arrange
    caplog.set_level("INFO")
    backup_date = restore_start_time - timedelta(days=1, hours=1)
    expected_backup = get_test_backup(backup_date)
    cloud_sql_backup_client_mock.get_latest_backup.return_value = expected_backup

    # Act & Assert
    with pytest.raises(RestoreCloudSQLInstanceError, match="Waiting for today backup more than 2 seconds, giving up"):
        restore_instance.try_get_today_backup(should_wait=True)

    assert "Found Cloud SQL latest backup from '2024-06-03 02:59:59+00:00'." in caplog.messages
    assert "Waiting for Cloud SQL instance backup created later than '2024-06-04 00:00:00+00:00'..." in caplog.messages


def test_restore_backup_with_on_demand_backup(
    restore_instance: RestoreCloudSQLInstance,
    restore_instance_config: RestoreCloudSQLInstanceConfig,
    new_cloud_sql_instance: CloudSQLInstance,
    cloud_sql_backup_client_mock: Mock,
    cloud_sql_instance_client_mock: Mock,
) -> None:
    # Arrange
    expected_backup = get_test_backup()
    cloud_sql_backup_client_mock.create_backup.return_value = expected_backup

    # Act
    actual_backup = restore_instance.restore_backup(new_cloud_sql_instance, create_on_demand_backup=True)

    # Assert
    assert actual_backup == expected_backup
    cloud_sql_backup_client_mock.create_backup.assert_called_once_with(
        instance_name=restore_instance_config.backup_instance_name,
        description=restore_instance_config.on_demand_backup_description,
        wait_for_completion=True,
    )
    cloud_sql_instance_client_mock.restore_backup.assert_called_once()
    cloud_sql_backup_client_mock.delete_backups.assert_called_once_with(
        instance_name=restore_instance_config.backup_instance_name,
        description=restore_instance_config.on_demand_backup_description,
        wait_for_completion=False,
    )


def test_restore_backup_without_on_demand_backup(
    restore_instance: RestoreCloudSQLInstance,
    restore_instance_config: RestoreCloudSQLInstanceConfig,
    new_cloud_sql_instance: CloudSQLInstance,
    cloud_sql_backup_client_mock: Mock,
    cloud_sql_instance_client_mock: Mock,
) -> None:
    # Arrange
    expected_backup = get_test_backup()
    cloud_sql_backup_client_mock.get_latest_backup.return_value = expected_backup

    # Act
    actual_backup = restore_instance.restore_backup(new_cloud_sql_instance, create_on_demand_backup=False)

    # Assert
    assert actual_backup == expected_backup
    cloud_sql_backup_client_mock.create_backup.assert_not_called()
    cloud_sql_instance_client_mock.restore_backup.assert_called_once()
    cloud_sql_backup_client_mock.delete_backups.assert_not_called()
    cloud_sql_backup_client_mock.get_latest_backup.assert_called_once_with(
        instance_name=restore_instance_config.backup_instance_name
    )


def get_test_backup(backup_start_time: datetime = BACKUP_START_TIME) -> Backup:
    backup_end_time = backup_start_time + timedelta(hours=1)

    return Backup(
        id="123",
        instance="test-instance",
        start_time=backup_start_time,
        end_time=backup_end_time,
        type=BackupType.ON_DEMAND,
        status="SUCCESSFUL",
    )
