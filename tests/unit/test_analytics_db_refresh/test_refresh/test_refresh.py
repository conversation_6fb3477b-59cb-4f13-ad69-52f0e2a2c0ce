from datetime import date, datetime, time
from unittest.mock import Mock, patch

import pytest
from _pytest.mark import param
from refresh import AnalyticsDbRefresh
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance
from refresh_shared.target_pg_repo import TableCount

from common.time_service import TimeService


@pytest.mark.parametrize("run_date", [param(date(2024, 11, 24), id="Sunday"), param(date(2024, 11, 25), id="Monday")])
@patch("refresh.migrate_db")
def test_run(
    migrate_db_mock: Mock,
    db_refresh_config: DbRefreshConfig,
    restore_instance: RestoreCloudSQLInstance,
    source_pg_repository_mock: Mock,
    target_pg_repository_mock: Mock,
    dumb_db_mock: Mock,
    restore_db_mock: Mock,
    mattermost_client_mock: Mock,
    run_date: date,
) -> None:
    # Arrange
    target_pg_repository_mock.select_from_function.return_value = [[1]]
    target_pg_repository_mock.get_refresh_tables_summary.return_value = [
        TableCount(table_name="foo", schema_name="bar", platform_db=1, analytics_db=0, diff=0)
    ]

    analytics_db_refresh = AnalyticsDbRefresh(
        config=db_refresh_config,
        restore_instance=restore_instance,
        source_db_repository=source_pg_repository_mock,
        target_db_repository=target_pg_repository_mock,
        pipeline_execution_db_repository=target_pg_repository_mock,
        dump_db=dumb_db_mock,
        restore_db=restore_db_mock,
        mattermost_client=mattermost_client_mock,
        time_service=TimeService(now=datetime.combine(run_date, time=time.min)),
    )

    # Act
    analytics_db_refresh.run()

    # Assert
    assert migrate_db_mock.call_count == 3  # 3 times: migrate source, fix target before switch, run final migration

    # Assert source db
    source_pg_repository_mock.drop_tables.assert_called_once()
    source_pg_repository_mock.clean_offers.assert_called_once()
    source_pg_repository_mock.persist_platform_tables_count.assert_called_once()
    source_pg_repository_mock.rename_schemas.assert_called_once()
    dumb_db_mock.dump.assert_called_once()

    # Assert target db
    assert target_pg_repository_mock.create_schemas.call_count == 2  # 2 times: temp schemas and backup schemas
    restore_db_mock.restore.assert_called_once()
    target_pg_repository_mock.refresh_materialized_views.assert_called_once()
    assert (
        target_pg_repository_mock.rename_schema.call_count == len(db_refresh_config.pg_dump_params.schema) * 2
    )  # 2x: temp schema and backup schema

    target_pg_repository_mock.revert_routines_schemas.assert_called_once()
    target_pg_repository_mock.load_platform_tables_count.assert_called_once()
    target_pg_repository_mock.load_analytics_tables_count.assert_called_once()
    mattermost_client_mock.send_message.assert_called_once()

    if run_date.isoweekday() == db_refresh_config.run_vacuum_analyze_weekday:
        target_pg_repository_mock.vacuum_database.assert_called_once()
    else:
        target_pg_repository_mock.vacuum_database.assert_not_called()
