import pytest

from src.sparky.sparky_model import IngestionTable


@pytest.mark.parametrize(
    "rows_count, expected",
    [
        (0, 1),
        (100_000, 1),
        (350_000, 2),
        (800_000, 3),
        (1_500_000, 5),
        (14_000_000, 470),
        (24_000_000, 800),
        (100_000_000, 3340),
        (500_000_000, 16670),
        (1_000_000_000, 33340),
    ],
)
def test_estimate_partition_number(rows_count: int, expected: int) -> None:
    table = IngestionTable(schema_name="test", table_name="test_table")

    assert table.estimate_partition_number(rows_count) == expected
