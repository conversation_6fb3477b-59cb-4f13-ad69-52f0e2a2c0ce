import pytest


@pytest.fixture(scope="session")
def test_parameter_id() -> str:
    return "integration-test-config"


@pytest.fixture(scope="session")
def test_secret_id() -> str:
    return "integration-test-secret"


@pytest.fixture(scope="session")
def config_expected() -> str:
    return """{"feature_a":"False","feature_b":"True","myapp":{"host":"localhost","port":8080}}"""


@pytest.fixture(scope="session")
def secret_expected() -> str:
    return "integration-test-secret-value"
