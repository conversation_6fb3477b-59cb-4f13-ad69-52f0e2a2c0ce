from common.config import STAGING_GOOGLE_PROJECT_ID, Config
from common.secret_manager_client import get_secret


def test_get_secret_by_id(test_secret_id: str, secret_expected: str, config: Config) -> None:
    parameter = get_secret(secret_id=test_secret_id, project_id=config.project_id)

    assert parameter is not None
    assert parameter == secret_expected


def test_get_parameter_by_project_id(test_secret_id: str, secret_expected: str, config: Config) -> None:
    secret_id = f"projects/{STAGING_GOOGLE_PROJECT_ID}/secrets/{test_secret_id}"

    parameter = get_secret(secret_id=secret_id, project_id=config.project_id)

    assert parameter is not None
    assert parameter == secret_expected
