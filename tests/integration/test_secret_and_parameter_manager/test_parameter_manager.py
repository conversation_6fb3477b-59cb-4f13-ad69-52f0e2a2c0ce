from common.config import STAGING_GOOGLE_PROJECT_ID, Config
from common.consts import GCP_REGION
from common.parameter_manager_client import get_parameter


def test_get_parameter_by_id(test_parameter_id: str, config_expected: str, config: Config) -> None:
    parameter = get_parameter(parameter_id=test_parameter_id, project_id=config.project_id)

    assert parameter is not None
    assert parameter == config_expected


def test_get_parameter_by_project_id(test_parameter_id: str, config_expected: str, config: Config) -> None:
    parameter_id = f"projects/{STAGING_GOOGLE_PROJECT_ID}/locations/{GCP_REGION}/parameters/{test_parameter_id}"

    parameter = get_parameter(parameter_id=parameter_id, project_id=config.project_id)

    assert parameter is not None
    assert parameter == config_expected
