import pytest

from common.mattermost_client import (
    Mattermost<PERSON><PERSON>,
    MattermostClientError,
    MattermostMessage,
)


def test_send_message_with_defaults(mattermost_client: MattermostClient) -> None:
    # arrange
    message = MattermostMessage(text="test_send_message_with_defaults")

    # act & assert
    mattermost_client.send_message(message)


def test_send_message_with_allowed_channel(mattermost_client: MattermostClient) -> None:
    # arrange
    message = MattermostMessage(text="test_send_message_with_allowed_channel", channel="mm-notification-test")

    # act & assert
    mattermost_client.send_message(message)


def test_send_message_with_disallowed_channel(mattermost_client: MattermostClient) -> None:
    # arrange
    message = MattermostMessage(text="test_send_message_with_disallowed_channel", channel="engineering--data--team")

    # act & assert
    with pytest.raises(
        MattermostClientError,
        match=r"Failed to handle the payload of media type application/json for incoming webhook.*",
    ):
        mattermost_client.send_message(message)


def test_send_message_with_username(mattermost_client: MattermostClient) -> None:
    # arrange
    message = MattermostMessage(text="test_send_message_with_username", username="john.doe")

    # act & assert
    mattermost_client.send_message(message)


def test_send_message_with_icon(mattermost_client: MattermostClient) -> None:
    # arrange
    message = MattermostMessage(text="test_send_message_with_icon", icon_emoji=":fire:")

    # act & assert
    mattermost_client.send_message(message)
