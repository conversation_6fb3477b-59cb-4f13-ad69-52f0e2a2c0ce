from dataclasses import replace

import pytest
from bm_shared.api import AlgoliaClient
from bm_shared.bm_model import AlgoliaSecret, BMConst
from bm_shared.oxy_http_client import OxyHTTPClient, ProxyConfig

from common.consts import STAGING_GOOGLE_PROJECT_ID
from common.secret_manager_client import get_secret


@pytest.fixture(scope="session")
def api_secret() -> AlgoliaSecret:
    api_secret = AlgoliaSecret.from_json(get_secret(BMConst.BM_ALGOLIA_SECRET_ID, STAGING_GOOGLE_PROJECT_ID))
    return api_secret


@pytest.fixture(scope="session")
def api_invalid_secret() -> AlgoliaSecret:
    api_secret = AlgoliaSecret.from_json(get_secret(BMConst.BM_ALGOLIA_SECRET_ID, STAGING_GOOGLE_PROJECT_ID))
    # invalidate api key for tests; turns out it requires to remove at least 2 characters
    api_secret = replace(api_secret, api_key=api_secret.api_key[:-2])
    return api_secret


@pytest.fixture(scope="session")
def oxy_client() -> OxyHTTPClient:
    oxy_secret = get_secret(BMConst.BM_OXY_PROXY_SECRET_ID, STAGING_GOOGLE_PROJECT_ID)
    return OxyHTTPClient(oxy_secret)


@pytest.fixture(scope="session")
def oxy_client_invalid_authorization() -> OxyHTTPClient:
    return OxyHTTPClient(ProxyConfig("test", "test"))


@pytest.fixture(scope="session")
def language_country() -> str:
    return "de-at"


@pytest.fixture(scope="session")
def iphone_filter() -> str:
    return '(cat_id:"2") AND (brand:"0  Apple") AND (model:"000 iPhone 13") AND special_offer_type=0'


@pytest.fixture(scope="session")
def algolia_client(oxy_client: OxyHTTPClient) -> AlgoliaClient:
    return AlgoliaClient(oxy_client)
