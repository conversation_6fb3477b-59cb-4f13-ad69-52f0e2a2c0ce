from datetime import date

import pytest
from google_ads_shared.google_ads_client import GoogleAdsClient
from google_ads_shared.google_ads_client_model import (
    AdvertisingChannelType,
    GoogleAdsAssetGroupMetrics,
    GoogleAdsCustomer,
)

from common.time_service import TimeService


@pytest.mark.parametrize(
    "name_like,expected_min_count,should_contain_name",
    [
        ("", 40, None),  # No filter - should return all customers (48)
        ("%CSS%", 10, "CSS"),  # Filter by "CSS" inside the name - should contain customers with "CSS" in name (14)
        ("%DE", 1, "DE"),  # Filter by "DE" at the end - should contain customers with "DE" in name (3)
        ("NonExistentCustomer", 0, None),  # Non-existent filter - should return empty list
    ],
)
def test_get_customers(
    google_ads_client: GoogleAdsClient, name_like: str, expected_min_count: int, should_contain_name: str | None
) -> None:
    """
    Test getting Google Ads customers with different name filters.

    :param google_ads_client: Google Ads client fixture
    :param name_like: Name filter to apply
    :param expected_min_count: Minimum expected number of customers
    :param should_contain_name: Expected substring in customer names (if any)
    """
    # Act
    customers = google_ads_client.get_customers(name_like=name_like)

    # Assert
    assert len(customers) >= expected_min_count

    # Validate that all returned objects are GoogleAdsCustomer instances
    for customer in customers:
        assert isinstance(customer, GoogleAdsCustomer)
        assert customer.id is not None and customer.id != ""
        assert customer.name is not None and customer.name != ""

        # If we expect a specific name substring, verify it's present
        if should_contain_name:
            assert (
                should_contain_name in customer.name
            ), f"Customer name '{customer.name}' should contain '{should_contain_name}'"


def test_get_costs(google_ads_client: GoogleAdsClient, time_service: TimeService) -> None:
    # Arrange
    customer = google_ads_client.get_customers(name_like="Refurbed Search | DACH")[0]
    assert customer is not None

    # Act
    costs = google_ads_client.get_costs(customer, time_service.days_ago(7), time_service.today)

    # Assert
    assert costs != []


def test_get_costs_raises_error_on_invalid_dates(google_ads_client: GoogleAdsClient) -> None:
    # Arrange
    today = date(2025, 7, 18)
    yesterday = date(2025, 7, 17)

    # Act & Assert
    with pytest.raises(AttributeError, match="Start date 2025-07-18 is after end date 2025-07-17!"):
        google_ads_client.get_costs(GoogleAdsCustomer(id="123", name="Test"), today, yesterday)


def test_get_asset_group_metrics_with_defaults(google_ads_client: GoogleAdsClient, time_service: TimeService) -> None:
    # Arrange
    customer = google_ads_client.get_customers(name_like="Refurbed CSS | DE")[0]
    assert customer is not None

    # Act
    metrics = google_ads_client.get_asset_group_metrics(customer, time_service.yesterday, time_service.today)

    # Assert
    assert len(metrics) > 0

    # Validate that all returned objects are GoogleAdsAssetGroupMetrics instances
    for metric in metrics:
        assert isinstance(metric, GoogleAdsAssetGroupMetrics)

        assert (metric.date == time_service.yesterday) or (metric.date == time_service.today)
        assert metric.customer_id == customer.id
        assert metric.campaign_name is not None and "PMax" in metric.campaign_name
        assert metric.asset_group_name is not None and metric.asset_group_name != ""

        assert isinstance(metric.impressions, int) and metric.impressions >= 0
        assert isinstance(metric.clicks, int) and metric.clicks >= 0
        assert isinstance(metric.costs, float) and metric.costs >= 0
        assert isinstance(metric.conversions, float) and metric.conversions >= 0
        assert isinstance(metric.conversions_value, float) and metric.conversions_value >= 0


@pytest.mark.parametrize(
    "advertising_channel_type,campaign_like,expected_min_count",
    [
        (AdvertisingChannelType.PERFORMANCE_MAX, "%PMax%", 100),  # Default filters
        (
            AdvertisingChannelType.PERFORMANCE_MAX,
            "",
            100,
        ),  # No campaign filter
        (AdvertisingChannelType.SEARCH, "", 0),  # Search campaigns
        (
            AdvertisingChannelType.PERFORMANCE_MAX,
            "NonExistentCampaign",
            0,
        ),  # Non-existent campaign
    ],
)
def test_get_asset_group_metrics_with_filters(
    google_ads_client: GoogleAdsClient,
    time_service: TimeService,
    advertising_channel_type: AdvertisingChannelType,
    campaign_like: str,
    expected_min_count: int,
) -> None:
    # Arrange
    customer = google_ads_client.get_customers(name_like="Refurbed CSS | DE")[0]
    assert customer is not None

    # Act
    metrics = google_ads_client.get_asset_group_metrics(
        customer,
        time_service.yesterday,
        time_service.today,
        advertising_channel_type=advertising_channel_type,
        campaign_like=campaign_like,
    )

    # Assert
    assert len(metrics) >= expected_min_count

    # Validate structure of returned metrics
    for metric in metrics:
        assert isinstance(metric, GoogleAdsAssetGroupMetrics)
        assert metric.customer_id == customer.id

        # Validate campaign name matches the filter pattern if specified
        if campaign_like != "%" and "NonExistent" not in campaign_like:
            campaign_pattern = campaign_like.replace("%", "")
            if campaign_pattern:
                assert (
                    campaign_pattern in metric.campaign_name
                ), f"Campaign name '{metric.campaign_name}' should contain '{campaign_pattern}'"


def test_get_asset_group_metrics_raises_error_on_invalid_dates(google_ads_client: GoogleAdsClient) -> None:
    # Arrange
    today = date(2025, 8, 14)
    yesterday = date(2025, 8, 13)
    customer = GoogleAdsCustomer(id="123", name="Test Customer")

    # Act & Assert
    with pytest.raises(AttributeError, match="Start date 2025-08-14 is after end date 2025-08-13!"):
        google_ads_client.get_asset_group_metrics(customer, today, yesterday)
