import pytest

from common.config import Config
from common.secret_manager_client import get_secret
from common.time_service import TimeService
from google_ads.google_ads_shared.google_ads_client import GoogleAdsClient
from google_ads.google_ads_shared.google_ads_client_model import (
    GOOGLE_ADS_SECRET_ID,
    GoogleAdsClientConfig,
)


@pytest.fixture(scope="session")
def time_service() -> TimeService:
    return TimeService()


@pytest.fixture(scope="session")
def google_ads_secret(config: Config) -> str:
    return get_secret(GOOGLE_ADS_SECRET_ID, config.project_id)


@pytest.fixture(scope="session")
def google_ads_client_config(google_ads_secret: str) -> GoogleAdsClientConfig:
    return GoogleAdsClientConfig.from_json(google_ads_secret)


@pytest.fixture(scope="session")
def google_ads_client(google_ads_client_config: GoogleAdsClientConfig) -> GoogleAdsClient:
    return GoogleAdsClient(config=google_ads_client_config)
