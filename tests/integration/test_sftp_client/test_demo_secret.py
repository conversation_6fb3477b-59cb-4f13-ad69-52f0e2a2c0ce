from common.config import STAGING_GOOGLE_PROJECT_ID, Config
from common.secret_manager_client import get_secret


def test_get_secret_by_id(demo_sftp_secret_id: str, demo_sftp_secret_value: str, config: Config) -> None:
    secret = get_secret(demo_sftp_secret_id, config.project_id)

    assert secret is not None
    assert secret == demo_sftp_secret_value


def test_get_secret_by_project_secret_id(demo_sftp_secret_id: str, demo_sftp_secret_value: str, config: Config) -> None:
    secret_id = f"projects/{STAGING_GOOGLE_PROJECT_ID}/secrets/{demo_sftp_secret_id}"
    secret = get_secret(secret_id, config.project_id)

    assert secret is not None
    assert secret == demo_sftp_secret_value
