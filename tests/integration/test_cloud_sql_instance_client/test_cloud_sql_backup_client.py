import pytest
from _pytest.logging import Log<PERSON>apture<PERSON>ixture

from common.cloud_sql_instance_client.backup_client import CloudSQLBackupClient
from common.cloud_sql_instance_client.client_base import CloudSQLClientError
from common.cloud_sql_instance_client.instance_model import BackupType
from common.consts import STAGING_ANALYTICS_SQL_INSTANCE


def test_get_backups(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # act
    actual_backups = cloud_sql_backup_client.get_backups(instance_name)

    # assert
    assert actual_backups != []


def test_get_latest_backup(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # act
    actual_backup = cloud_sql_backup_client.get_latest_backup(instance_name)

    # assert
    assert actual_backup is not None


def test_describe_backup(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # Get the latest backup to use its ID
    latest_backup = cloud_sql_backup_client.get_latest_backup(instance_name)
    assert latest_backup is not None

    # act
    backup = cloud_sql_backup_client.describe_backup(instance_name, latest_backup.id)

    # assert
    assert backup is not None
    assert backup.id == latest_backup.id
    assert backup.instance == instance_name


def test_describe_non_existing_backup(cloud_sql_backup_client: CloudSQLBackupClient, caplog: LogCaptureFixture) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    backup_id = "999999999"

    # act
    backup = cloud_sql_backup_client.describe_backup(instance_name, "999999999")

    # assert
    assert backup is None
    assert caplog.records[0].levelname == expected_logger_level
    assert f"Backup with ID '{backup_id}' not found!" in caplog.messages


@pytest.mark.skip(reason="Creates a real backup of a Cloud SQL instance")
def test_create_delete_backup(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Test backup created by integration test"

    # act & assert - create backup
    backup = cloud_sql_backup_client.create_backup(instance_name, description)
    assert backup is not None
    assert backup.instance == instance_name
    assert backup.description == description
    assert backup.type == BackupType.ON_DEMAND

    # act & assert - delete backup
    cloud_sql_backup_client.delete_backup(instance_name, backup.id)

    # Verify backup is deleted by checking it's not in the list of backups
    backups = cloud_sql_backup_client.get_backups(instance_name)
    assert not any(b.id == backup.id for b in backups)


def test_delete_non_existing_backup(cloud_sql_backup_client: CloudSQLBackupClient, caplog: LogCaptureFixture) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    backup_id = "999999999"

    # act & assert
    cloud_sql_backup_client.delete_backup(instance_name, backup_id)

    # assert
    assert caplog.records[0].levelname == expected_logger_level
    assert f"Backup with ID '{backup_id}' not found!" in caplog.messages


@pytest.mark.skip(reason="Creates and deletes real backups of a Cloud SQL instance")
def test_delete_backups(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Test backup created for delete_backups test"

    # Create multiple backups with the same description
    backup1 = cloud_sql_backup_client.create_backup(instance_name, description)
    backup2 = cloud_sql_backup_client.create_backup(instance_name, description)

    assert backup1 is not None
    assert backup2 is not None
    assert backup1.description == description
    assert backup2.description == description
    assert backup1.type == BackupType.ON_DEMAND
    assert backup2.type == BackupType.ON_DEMAND

    # act
    deleted_count = cloud_sql_backup_client.delete_backups(instance_name, description)

    # assert
    assert deleted_count >= 2  # At least the two backups created

    # Verify backups are deleted by checking they're not in the list of backups
    backups = cloud_sql_backup_client.get_backups(instance_name)
    assert not any(b.id == backup1.id for b in backups)
    assert not any(b.id == backup2.id for b in backups)


def test_delete_backups_empty_description(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = ""

    with pytest.raises(CloudSQLClientError, match="Cannot specify empty description!"):
        cloud_sql_backup_client.delete_backups(instance_name, description)


def test_delete_backups_no_matching_backups(
    cloud_sql_backup_client: CloudSQLBackupClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Non-existent backup description"

    # act
    deleted_count = cloud_sql_backup_client.delete_backups(instance_name, description)

    # assert
    assert deleted_count == 0
    assert caplog.records[0].levelname == expected_logger_level
    assert (
        f"No '{BackupType.ON_DEMAND}' backups with description '{description}' found for instance '{instance_name}'"
        in caplog.messages
    )


@pytest.mark.skip(reason="Creates and deletes real backups of a Cloud SQL instance")
def test_delete_backups_preserves_empty_description(cloud_sql_backup_client: CloudSQLBackupClient) -> None:
    # arrange
    instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    none_description = None
    empty_description = ""
    non_empty_description = "Test backup with non-empty description"

    # Create a backup with None description
    backup_none_description = cloud_sql_backup_client.create_backup(instance_name, none_description)
    # Create a backup with empty description
    backup_empty_description = cloud_sql_backup_client.create_backup(instance_name, empty_description)
    # Create a backup with non-empty description
    backup_non_empty = cloud_sql_backup_client.create_backup(instance_name, non_empty_description)

    assert backup_none_description is not None
    assert backup_empty_description is not None
    assert backup_non_empty is not None
    assert backup_none_description.description is None
    assert backup_empty_description.description is None
    assert backup_non_empty.description == non_empty_description

    assert backup_none_description.type == BackupType.ON_DEMAND
    assert backup_empty_description.type == BackupType.ON_DEMAND
    assert backup_non_empty.type == BackupType.ON_DEMAND

    # act - delete backups with non-empty description
    deleted_count = cloud_sql_backup_client.delete_backups(instance_name, non_empty_description)

    # assert
    assert deleted_count >= 1  # At least the one backup with non-empty description

    # Verify only the backup with non-empty description is deleted
    backups = cloud_sql_backup_client.get_backups(instance_name)
    assert any(b.id == backup_empty_description.id for b in backups)  # Backup with empty description should still exist
    assert not any(b.id == backup_non_empty.id for b in backups)  # Backup with non-empty description should be deleted

    # Clean up - delete the backup with empty description manually
    cloud_sql_backup_client.delete_backup(instance_name, backup_empty_description.id, False)
