import pytest
from _pytest.logging import LogCaptureFixture

from common.cloud_sql_instance_client.backup_client import CloudSQLBackupClient
from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    CloudSQLRestoreBackupParams,
    InstanceCreateSettings,
    IpConfiguration,
    RestoreBackupContext,
)
from common.consts import (
    PROD_ANALYTICS_PROJECT_NAME,
    PROD_ANALYTICS_SQL_INSTANCE,
    STAGING_ANALYTICS_SQL_INSTANCE,
    STAGING_GOOGLE_PROJECT_ID,
)


@pytest.mark.skip(reason="Creates a real CLoud SQL instance")
def test_create_restore_drop_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient,
    cloud_sql_backup_client: CloudSQLBackupClient,
    new_cloud_sql_instance_params: CloudSQLCreateInstanceParams,
) -> None:
    # arrange
    staging_instance_name = STAGING_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    staging_latest_backup = cloud_sql_backup_client.get_latest_backup(staging_instance_name)
    assert staging_latest_backup is not None

    backup_params = CloudSQLRestoreBackupParams(
        restore_backup_context=RestoreBackupContext(
            instance_id=staging_instance_name, project=STAGING_GOOGLE_PROJECT_ID, backup_run_id=staging_latest_backup.id
        )
    )

    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=new_cloud_sql_instance_params)
    assert actual_instance.name == new_cloud_sql_instance_params.name
    assert new_cloud_sql_instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act
    cloud_sql_instance_client.restore_backup(instance_name=actual_instance.name, backup_params=backup_params)

    # act & assert
    cloud_sql_instance_client.drop_instance(name=actual_instance.name)
    instance = cloud_sql_instance_client.describe_instance(name=actual_instance.name)
    assert instance is None


@pytest.mark.skip(reason="Restore a real production backup into big CLoud SQL instance")
def test_create_instance_restore_from_production(
    cloud_sql_instance_client: CloudSQLInstanceClient, cloud_sql_backup_client: CloudSQLBackupClient
) -> None:
    # arrange
    project_id = PROD_ANALYTICS_PROJECT_NAME
    prod_instance_name = PROD_ANALYTICS_SQL_INSTANCE  # real name of Cloud SQL instance on production
    prod_latest_backup = cloud_sql_backup_client.get_latest_backup(prod_instance_name)
    assert prod_latest_backup is not None

    backup_params = CloudSQLRestoreBackupParams(
        restore_backup_context=RestoreBackupContext(
            instance_id=prod_instance_name, project=project_id, backup_run_id=prod_latest_backup.id
        )
    )

    instance_params = CloudSQLCreateInstanceParams(
        name="analytics-db-prod-restore",
        root_password="Test-123@",
        settings=InstanceCreateSettings(
            tier="db-custom-32-65536",  # 32 CPUs, 64 GB RAM
            data_disk_size_gb=600,
            backup_configuration=BackupConfiguration(),
            ip_configuration=IpConfiguration(),
        ),
    )

    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=instance_params)
    assert actual_instance.name == instance_params.name
    assert instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act
    cloud_sql_instance_client.restore_backup(instance_name=actual_instance.name, backup_params=backup_params)


@pytest.mark.skip(reason="Creates a real CLoud SQL instance")
def test_create_stop_start_drop_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient, new_cloud_sql_instance_params: CloudSQLCreateInstanceParams
) -> None:
    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=new_cloud_sql_instance_params)
    assert actual_instance.name == new_cloud_sql_instance_params.name
    assert new_cloud_sql_instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act & assert
    stopped_instance = cloud_sql_instance_client.stop_instance(instance_name=actual_instance.name)
    assert stopped_instance.settings.activation_policy == "NEVER"

    # act & assert
    started_instance = cloud_sql_instance_client.start_instance(instance_name=actual_instance.name)
    assert started_instance.settings.activation_policy == "ALWAYS"

    # act & assert
    cloud_sql_instance_client.drop_instance(name=actual_instance.name)
    instance = cloud_sql_instance_client.describe_instance(name=actual_instance.name)
    assert instance is None


def test_describe_existing_instance(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # act
    actual_instance = cloud_sql_instance_client.describe_instance(name=STAGING_ANALYTICS_SQL_INSTANCE)

    # assert
    assert actual_instance.name == STAGING_ANALYTICS_SQL_INSTANCE


def test_describe_non_existing_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "INFO"
    caplog.set_level(expected_logger_level)
    instance_name = "non-existing-instance"

    # act
    actual_instance = cloud_sql_instance_client.describe_instance(name=instance_name)

    # assert
    assert actual_instance is None
    assert caplog.records[0].levelname == expected_logger_level
    assert f"The Cloud SQL instance '{instance_name}' does not exist." in caplog.messages


def test_get_instances(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # act
    actual_instances = cloud_sql_instance_client.get_instances()

    # assert
    assert STAGING_ANALYTICS_SQL_INSTANCE in [instance.name for instance in actual_instances]
