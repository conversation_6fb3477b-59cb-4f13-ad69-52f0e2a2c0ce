from typing import Iterator

import pytest
from pandas import DataFrame
from sqlalchemy import Connection

from common.sql_model import SqlProcedure, SqlTable
from common.sql_repository import SqlRepository


@pytest.fixture(scope="function")
def transaction(sql_repository: SqlRepository) -> Iterator[Connection]:
    with sql_repository.begin_transaction() as transaction:
        yield transaction
        transaction.rollback()


@pytest.mark.parametrize(
    "sql_procedure, sql_table",
    [
        (
            SqlProcedure(schema_name="export_etl", procedure_name="reload_full_order_item_refunds"),
            SqlTable(schema_name="analytics", table_name="full_order_item_refunds"),
        ),
        (
            SqlProcedure(schema_name="export_etl", procedure_name="load_order_item_exchange_rate"),
            SqlTable(schema_name="analytics", table_name="order_item_exchange_rate"),
        ),
    ],
)
def test_proc_executor_procedures(
    sql_repository: SqlRepository,
    transaction: Connection,
    order_item_offers: DataFrame,
    order_item_offers_table: SqlTable,
    sql_procedure: SqlProcedure,
    sql_table: SqlTable,
) -> None:
    """
    Test stored procedures used in `analytics.yaml` workflow and run by `proc_executor`.
    """
    # arrange
    sql_repository.insert(df=order_item_offers, table=order_item_offers_table, truncate=True, transaction=transaction)
    sql_repository.truncate_table(table=sql_table, transaction=transaction)

    # act
    sql_repository.call(procedure=sql_procedure, transaction=transaction)

    # assert
    result = sql_repository.select(table=sql_table, transaction=transaction)
    assert len(result) > 0
