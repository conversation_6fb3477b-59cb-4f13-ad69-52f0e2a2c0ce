from pathlib import Path

import pytest
from integration.sql_schema import USER, User, assert_users, insert_user
from pandas import DataFrame
from sqlalchemy import Engine

from common.config import DatabaseConfig
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


@pytest.fixture(scope="function")
def query_file(sql_dir: Path) -> Path:
    return sql_dir / "test_query.sql"


def test_sql_repository_engine(sql_repository: SqlRepository, engine: Engine, db_config: DatabaseConfig) -> None:
    assert sql_repository is not None
    assert sql_repository.engine.url == engine.url
    assert sql_repository.db_config == db_config


def test_sql_repository_select_from_query_file(sql_repository: SqlRepository, query_file: Path) -> None:
    # arrange
    zero = "0"

    # act
    result = sql_repository.select_from_query_file(query_file, version=zero)

    # assert
    all_rows = result.fetchall()
    assert len(all_rows) == 1
    assert all_rows[0].version == zero


@pytest.mark.parametrize(
    "truncate",
    [
        False,
        True,
    ],
)
def test_sql_repository_insert_empty_df(sql_repository: SqlRepository, sql_table: SqlTable, truncate: bool) -> None:
    # arrange
    df = DataFrame(columns=USER.columns)

    insert_user(sql_repository.engine, sql_table, USER)
    assert_users(sql_repository, sql_table, [USER])
    expected = [] if truncate else [USER]

    # act
    result = sql_repository.insert(df=df, table=sql_table, truncate=truncate)

    # assert
    assert result == 0
    assert_users(sql_repository, sql_table, expected)


@pytest.mark.parametrize(
    "truncate",
    [
        False,
        True,
    ],
)
def test_sql_repository_insert_df(sql_repository: SqlRepository, sql_table: SqlTable, truncate: bool) -> None:
    # arrange
    user2 = User(2, "test2")
    df = DataFrame.from_dict({user2.columns[0]: [user2.id], user2.columns[1]: [user2.username]})

    insert_user(sql_repository.engine, sql_table, USER)
    assert_users(sql_repository, sql_table, [USER])
    expected = [user2] if truncate else [USER, user2]

    # act
    result = sql_repository.insert(df=df, table=sql_table, truncate=truncate)

    # assert
    assert result == 1
    assert_users(sql_repository, sql_table, expected)
