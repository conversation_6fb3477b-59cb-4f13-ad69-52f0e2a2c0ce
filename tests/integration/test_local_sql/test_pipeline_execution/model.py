import logging
from dataclasses import dataclass
from datetime import datetime
from functools import cached_property
from typing import Optional

from dataclasses_json import DataClassJsonMixin

from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_repository import SqlRepository
from common.time_service import TimeService
from common.utils import get_run_id, to_space_separated

logger = logging.getLogger()


@dataclass(frozen=True)
class PipelineExecution(DataClassJsonMixin):
    id: int
    pipeline_name: str
    run_id: str
    started: datetime
    finished: datetime
    duration: str
    seconds: float
    succeeded: bool
    error: str


@dataclass(frozen=True)
class PipelineExecutionStep(PipelineExecution):
    step_id: int
    step_name: str


class PipelineExecutionTestClass(SupportsPipelineExecution):
    def __init__(self, base_number: int, sql_repo: SqlRepository, time_service: Optional[TimeService] = None):
        self._base_number = base_number
        self._sql_repo = sql_repo
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def pipeline_name(self) -> str:
        """Pipeline name"""

        return to_space_separated(self.__class__.__name__)

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""

        return get_run_id(self._time_service.now)

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository used by `log_pipeline_execution` decorator"""

        return self._sql_repo

    @log_pipeline_execution(start=True, end=True)
    def run(self, multiplier: int, x: str, y: int) -> int:
        self.dummy(self._base_number)
        results = self.multiply(multiplier, x, y)

        return results

    @log_pipeline_execution()
    def multiply(self, multiplier: int, x: str, y: int) -> int:
        logger.info(f"Some extra parameters {x=}, {y=}")

        if not isinstance(multiplier, int):
            raise ValueError(f"Multiplier must be an integer, got {type(multiplier)}")

        return self._base_number * multiplier

    @log_pipeline_execution()
    def dummy(self, value_to_pass: int) -> int:
        return value_to_pass
