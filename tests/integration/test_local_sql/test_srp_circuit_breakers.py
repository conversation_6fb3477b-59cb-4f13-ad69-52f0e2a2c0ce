"""
For this integration test to work:
- The views depend on active merchants view
- It's impossible grind to add test merchant data since there 68 non-null columns of various types you need to fill in
- So I checked some active merchants there were already in the local db image and changed manually the IDs in the
  test data CSV to match
- For the test data on the CSV
  - Get correct test data per condition rate
    (most important is date, since each circuit breaker has different date filter)
  - And in the actual test function, we need to change the date to the relevant date per circuit breaker
"""

from datetime import date
from pathlib import Path
from typing import Iterator

import pytest
from circuit_breaker_repository import CircuitBreakersPgRepository
from circuit_breakers_model import CircuitBreakerSchema
from pandas import DataFrame, Timedelta, Timestamp, read_csv, to_timedelta
from sqlalchemy import Connection
from utils import assert_data_frame_schema

from common.config import DatabaseConfig
from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlFunction, SqlTable

MPS_PER_COUNTRY_COMBINED_HISTORY = SqlTable(schema_name="analytics", table_name="mps_per_country_combined_history")
PIPEDRIVE = SqlTable(schema_name="analytics", table_name="pipedrive")


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig) -> CircuitBreakersPgRepository:
    return CircuitBreakersPgRepository(db_config)


@pytest.fixture(scope="function")
def transaction(repository: CircuitBreakersPgRepository) -> Iterator[Connection]:
    with repository.begin_transaction() as transaction:
        yield transaction
        transaction.rollback()


@pytest.fixture(scope="session")
def mps_data(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "mps_per_country_combined_history_data.csv",
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
        # Date types
        parse_dates=["date", "first_order_date"],
    )


@pytest.fixture(scope="function")
def pipedrive(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "pipedrive_circuit_breakers.csv",
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.mark.parametrize(
    "date_field, first_order_date, circuit_breaker, merchant_id",
    [
        # Response time
        (
            (Timestamp.now() - Timedelta(days=2)).date(),
            Timestamp.now() - Timedelta(days=62),
            CircuitBreakersPgRepository.response,
            437,
        ),
        # CSAT score
        (
            (Timestamp.now() - Timedelta(days=2)).date(),
            Timestamp.now() - Timedelta(days=62),
            CircuitBreakersPgRepository.csat,
            295,
        ),
        # Defect score -- normal merchant (no sda merchant in test data)
        (
            (Timestamp.now() - Timedelta(days=2)).date(),
            Timestamp.now() - Timedelta(days=62),
            CircuitBreakersPgRepository.defect,
            126,
        ),
        # Condition score -- Keeping this last to be able to test through main srp_circuit_breakers
        (
            (Timestamp.now() - to_timedelta(Timestamp.now().weekday(), unit="d") - Timedelta(days=2)).date(),
            Timestamp.now() - Timedelta(days=62),
            CircuitBreakersPgRepository.condition,
            584,
        ),
    ],
)
def test_select_circuit_breaker_queries(
    mps_data: DataFrame,
    pipedrive: DataFrame,
    repository: CircuitBreakersPgRepository,
    transaction: Connection,
    date_field: date,
    first_order_date: date,
    circuit_breaker: SqlFunction,
    merchant_id: int,
) -> None:
    # arrange: insert test data to match 1 row from test data, according to circuit breakers views definitions
    mps_data["date"] = date_field
    mps_data["first_order_date"] = first_order_date
    repository.insert(df=mps_data, table=MPS_PER_COUNTRY_COMBINED_HISTORY, transaction=transaction, truncate=True)

    # Insert required pipedrive data
    pipedrive["has_performance_role"] = pipedrive["has_performance_role"].map({"t": True, "f": False})
    repository.insert(df=pipedrive, table=PIPEDRIVE, transaction=transaction, truncate=True)

    expected_df = mps_data[mps_data["id"] == merchant_id][["id", "name"]].rename(
        columns={"id": "merchant_id", "name": "merchant_name"}
    )

    # Get relevant emails from pipedrive
    email_df = pipedrive[(pipedrive["merchant_id"] == merchant_id) & (pipedrive["has_performance_role"])][
        ["merchant_id", "primary_email"]
    ]

    # Merge the dataframes to create the expected result
    expected_df = expected_df.merge(email_df, on="merchant_id", how="inner").reset_index(drop=True)

    expected = DataFrameWithSchema(expected_df, schema=CircuitBreakerSchema)

    # act
    actual = repository.select_circuit_breaker_data(circuit_breaker, transaction)

    # assert
    assert_data_frame_schema(actual, expected)
