from pathlib import Path
from typing import Iterator

import pytest
from model import PipedriveData
from pandas import DataFrame, read_csv
from pandas._testing import assert_frame_equal
from pg_repository import ZendeskAutoResponderPgRepository

from common.config import DatabaseConfig
from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlTable

PIPEDRIVE = SqlTable(schema_name="analytics", table_name="pipedrive")
MISSING_PIPEDRIVE = SqlTable(schema_name="analytics", table_name="missing_pipedrive")


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig) -> Iterator[ZendeskAutoResponderPgRepository]:
    repository = ZendeskAutoResponderPgRepository(db_config)
    yield repository

    repository.truncate_table(table=PIPEDRIVE)
    repository.truncate_table(table=MISSING_PIPEDRIVE)


@pytest.fixture(scope="function")
def pipedrive(data_dir: Path) -> DataFrame:
    return read_csv(data_dir / "pipedrive.csv")


@pytest.fixture(scope="function")
def missing_pipedrive(data_dir: Path) -> DataFrame:
    return read_csv(data_dir / "missing_pipedrive.csv")


@pytest.fixture(scope="function")
def expected(data_dir: Path) -> DataFrame:
    return read_csv(data_dir / "expected_zendesk_auto_responder.csv")


@pytest.mark.skip(reason="To be fixed by https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/461")
def test_select_pipedrive_data(
    repository: ZendeskAutoResponderPgRepository,
    pipedrive: DataFrame,
    missing_pipedrive: DataFrame,
    expected: DataFrame,
) -> None:
    # arrange: insert test data
    # Convert 't' and 'f' strings to boolean in the 'has_performance_role' column
    pipedrive["has_performance_role"] = pipedrive["has_performance_role"].map({"t": True, "f": False})
    repository.insert(df=pipedrive, table=PIPEDRIVE, truncate=True)
    repository.insert(df=missing_pipedrive, table=MISSING_PIPEDRIVE, truncate=True)

    # Apply the schema to the extracted results
    expected = DataFrameWithSchema(dataframe=expected, schema=PipedriveData.SCHEMA).dataframe_with_schema
    expected = expected.sort_values(by=[PipedriveData.MERCHANT_ID, PipedriveData.PRIMARY_EMAIL]).reset_index(drop=True)

    # act
    actual = repository.select_pipedrive_date().dataframe_with_schema
    actual = actual.sort_values(by=[PipedriveData.MERCHANT_ID, PipedriveData.PRIMARY_EMAIL]).reset_index(drop=True)

    # assert
    assert_frame_equal(actual, expected)
