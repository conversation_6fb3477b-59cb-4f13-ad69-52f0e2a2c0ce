import pytest
from cs_model import CATEGORY_SLUGS_SCHEMA, COUNTRIES_SCHEMA

from category_slug_mapper.cs_pg_repository import CategorySlugsPGRepository
from common.config import DatabaseConfig


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig) -> CategorySlugsPGRepository:
    return CategorySlugsPGRepository(db_config)


@pytest.fixture(scope="function")
def expected_slugs() -> set[str]:
    """The list is specific to the `platform-db` we are using as a base image"""
    return {
        "slug",
        "slug_cs",
        "slug_da",
        "slug_en",
        "slug_fi",
        "slug_fr",
        "slug_it",
        "slug_nl",
        "slug_pl",
        "slug_pt",
        "slug_sl",
        "slug_sv",
    }


def test_category_slugs(repository: CategorySlugsPGRepository, expected_slugs: set[str]) -> None:
    expected_columns = set(CATEGORY_SLUGS_SCHEMA.columns).union(expected_slugs)

    repository.drop_category_slugs()
    repository.create_category_slugs()

    actual = repository.select_category_slugs()

    assert not actual.dataframe.empty
    assert len(actual.dataframe) > 0
    assert actual.schema == CATEGORY_SLUGS_SCHEMA

    actual_columns = set(actual.dataframe.columns)
    assert actual_columns == expected_columns


def test_select_countries(repository: CategorySlugsPGRepository) -> None:
    actual = repository.select_countries()

    assert not actual.dataframe.empty
    assert len(actual.dataframe) > 0
    assert actual.schema == COUNTRIES_SCHEMA
