from typing import Iterator

import pytest
from pandas import DataFrame
from pipedrive_model import PipedriveSchema, PipedriveTransformed
from pipedrive_pg_repo import PipedrivePgRepository

from common.config import DatabaseConfig
from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlTable

SQL_TABLE = SqlTable(schema_name="analytics", table_name="pipedrive")


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig) -> Iterator[PipedrivePgRepository]:
    repository = PipedrivePgRepository(db_config)

    yield repository

    repository.truncate_table(table=SQL_TABLE)


def test_reload_table(repository: PipedrivePgRepository, pipedrive_transformed: list[PipedriveTransformed]) -> None:
    given = DataFrameWithSchema(schema=PipedriveSchema.SCHEMA, dataframe=DataFrame(pipedrive_transformed))

    # act
    repository.reload_table(given)
    actual = repository.select_table()

    # assert
    assert actual == pipedrive_transformed
