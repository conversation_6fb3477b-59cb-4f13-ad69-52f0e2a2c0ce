# Performance marketing Google Ads Steering

## !! Rerun ONLY between :35 and :55 MAX of the hour !!
- !! Don't rerun outside that window, since the steering numbers will be wrong !!

## For local development
Install local environment
- `pyenv local 3.12`
- `python -m venv .venv`
- `source .venv/bin/activate`
- `pip install -r requirements_dev.txt`
- `pip install -r requirements.txt`

## Execution
- Run `main.py` since the `__main__()` only grabs data and reports, doesn't update!
- ⚠️ Run the `create_promotion_extensions_creation.py` only if you are told by <PERSON>, updates production Google Ads