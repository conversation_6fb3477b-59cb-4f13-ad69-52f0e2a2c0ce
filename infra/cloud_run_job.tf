module "alerting_check_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "alerting-job-check"
  job_root_path         = "../src/alerting_check"
  service_account_email = module.alerting_sa.sa_email

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "analytics_db_refresh_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "analytics-db-refresh"
  job_root_path         = "../src/analytics_db_refresh"
  job_resources_path    = "../src/sql-pg"
  docker_file_path      = "../src/analytics_db_refresh/resources/Dockerfile"
  service_account_email = module.analytics_db_refresh_sa.sa_email
  bucket_volume         = { bucket_name = module.backup_bucket.bucket_name, mount_path = "/tmp/" }

  container_cpu    = 8
  container_memory = "32Gi"
  job_timeout      = "21600s" # 6 hours

  env_variables = merge(local.runtime_env_variables, {
    SQL_MIGRATIONS_PATH = "/app/resources/sql-pg"
  })
  secret_variables = { PGPASSWORD = module.analytics_db_refresh_source_pgpassword_secret.secret_id }

  labels = local.p1_labels
}

module "pipedrive_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "pipedrive-etl-job"
  job_root_path         = "../src/pipedrive"
  service_account_email = module.pipedrive_sa.sa_email

  env_variables = merge(local.runtime_env_variables, {
    PIPEDRIVE_API_SECRET = module.pipedrive_api_secret.secret_id
  })

  labels = local.p2_labels
}

module "bm_api_worker_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "backmarket-product-worker-job"
  job_root_path         = "../src/backmarket/api_worker"
  job_shared_path       = "../src/backmarket/bm_shared"
  service_account_email = module.bm_api_worker_sa.sa_email

  container_memory = "2Gi"
  job_timeout      = "3000s" # 50 minutes

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "platform_export_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "platform-export-job"
  job_root_path         = "../src/platform_export/etl"
  job_shared_path       = "../src/platform_export/export_shared"
  service_account_email = module.platform_export_sa.sa_email

  container_memory = "1Gi"
  job_timeout      = "10800s" # 3 hours

  env_variables = merge(local.runtime_env_variables, {
    MAX_BYTES_BILLED = "************", # 200 GB
    TIMEOUT_IN_SEC   = "10800",        # 3 hours
  })

  labels = local.p2_labels
}

module "bm_compaction_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "backmarket-compaction-job"
  job_root_path         = "../src/backmarket/product_compaction"
  job_shared_path       = "../src/backmarket/bm_shared"
  service_account_email = module.bm_compaction_sa.sa_email

  container_memory = "4Gi"
  job_timeout      = "900s" # 15 minutes

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}


module "revenue_sync_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "revenue-sync-job"
  job_root_path         = "../src/performance_marketing/revenue_sync"
  service_account_email = module.revenue_sync_sa.sa_email
  max_retries           = 3

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "google_ads_etl_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "google-ads-etl-job"
  job_root_path         = "../src/performance_marketing/google_ads_etl"
  service_account_email = module.google_ads_etl_sa.sa_email
  max_retries           = 3

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "microsoft_ads_etl_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "microsoft-ads-etl-job"
  job_root_path         = "../src/performance_marketing/microsoft_ads_etl"
  service_account_email = module.microsoft_ads_etl_sa.sa_email
  max_retries           = 3

  env_variables = local.runtime_env_variables

  labels = merge(local.p2_labels, { backup_owner = "@bill_mourtakos" })
}

module "migrate_pg_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "migrate-pg"
  job_root_path         = "../src/migrate_db"
  job_resources_path    = "../src/sql-pg"
  docker_file_path      = "../src/migrate_db/Dockerfile"
  service_account_email = module.migrate_pg_sa.sa_email

  job_timeout = "3600s" # 1 hour

  env_variables = merge(local.runtime_env_variables, {
    SQL_MIGRATIONS_PATH = "/app/resources",
    MIGRATION_TARGET    = "POSTGRES",
  })

  labels = local.p1_labels
}

module "migrate_bq_job" {
  source                = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_job?ref=main"
  force_deploy          = var.force_deploy
  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url

  job_name              = "migrate-bq"
  job_root_path         = "../src/migrate_db"
  job_resources_path    = "../src/sql-bq"
  docker_file_path      = "../src/migrate_db/Dockerfile"
  service_account_email = module.migrate_bq_sa.sa_email

  job_timeout = "3600s" # 1 hour

  env_variables = merge(local.runtime_env_variables, {
    SQL_MIGRATIONS_PATH = "/app/resources",
    MIGRATION_TARGET    = "BIGQUERY",
  })

  labels = local.p1_labels
}
