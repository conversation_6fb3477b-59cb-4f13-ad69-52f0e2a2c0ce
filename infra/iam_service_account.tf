locals {
  big_query_base_path = "projects/${local.project.id}/datasets"
  big_query = {
    datasets = {
      raw              = "${local.big_query_base_path}/analytics_raw"
      transformed      = "${local.big_query_base_path}/analytics_transformed"
      reporting        = "${local.big_query_base_path}/analytics_reporting"
      ga4              = "${local.big_query_base_path}/analytics_304326635"
      google_analytics = "${local.big_query_base_path}/google_analytics"
      maintenance      = "${local.big_query_base_path}/maintenance"
      funnelio         = "${local.big_query_base_path}/funnelio"
      platform_export  = "${local.big_query_base_path}/platform_export"
    }
  }

  workflow_roles = [
    "roles/workflows.invoker",
    "roles/run.invoker",
    "roles/run.jobsExecutorWithOverrides"
  ]
  workflow_permissions = ["logging.logEntries.create", "run.executions.get"]

  sql_instance_permissions = [
    "cloudsql.instances.create",
    "cloudsql.instances.delete",
    "cloudsql.instances.restoreBackup",
    "cloudsql.operations.get",
    "cloudsql.operations.list",
  ]

  sql_backup_permissions = [
    "cloudsql.backupRuns.list",
    "cloudsql.backupRuns.get",
    "cloudsql.backupRuns.create",
    "cloudsql.backupRuns.update",
    "cloudsql.backupRuns.delete",
    "cloudsql.instances.list",
    "cloudsql.instances.get",
    "cloudsql.operations.get",
    "cloudsql.operations.list",
  ]
}

module "cicd_gitlab" {
  # Manually added `roles/bigquery.admin` to `refb-analytics-funnelio` project
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "gitlab-cicd"
  predefined_roles = [
    {
      roles = [
        "roles/iam.roleAdmin",
        "roles/iam.serviceAccountUser",
        "roles/iam.serviceAccountAdmin",
        "roles/run.admin",
        "roles/cloudfunctions.admin",
        "roles/resourcemanager.projectIamAdmin",
        "roles/storage.admin",
        "roles/cloudsql.admin",
        "roles/secretmanager.admin",
        "roles/parametermanager.admin",
        "roles/pubsub.admin",
        "roles/cloudscheduler.admin",
        "roles/artifactregistry.admin",
        "roles/artifactregistry.repoAdmin",
        "roles/bigquery.admin",
        "roles/logging.admin",
        "roles/monitoring.admin",
        "roles/workflows.admin"
      ]
    }
  ]

  custom_permissions = [
    {
      permissions = [
        "iam.roles.get",
        "iam.workloadIdentityPools.get",
        "iam.workloadIdentityPoolProviders.get",
        "iam.workloadIdentityPoolProviders.update",
        "iam.serviceAccounts.getIamPolicy",
        "iam.serviceAccounts.getAccessToken",
        "resourcemanager.projects.setIamPolicy",
        "resourcemanager.projects.getIamPolicy"
      ]
    }
  ]
}

module "artifact_registry_gitlab" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name          = "artifact-registry-gitlab"
  predefined_roles = [{ roles = ["roles/artifactregistry.reader"] }]
}

module "alerting_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name          = "alerting-sa"
  predefined_roles = [{ roles = ["roles/logging.logWriter", "roles/logging.viewer"] }]
  custom_permissions = [
    {
      permissions = [
        "run.jobs.get", "run.services.get", "cloudfunctions.functions.get", "run.executions.get", "run.executions.list"
      ]
    }
  ]
}

module "google_analytics_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "google-analytics-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.google_analytics, local.big_query.datasets.ga4]
    }
  ]
}

module "platform_export_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "platform-export-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser", "roles/bigquery.connectionUser"]
    },
    {
      roles = ["roles/bigquery.dataEditor"],
      resource_names = [
        local.big_query.datasets.raw,
        local.big_query.datasets.transformed,
        local.big_query.datasets.platform_export
      ]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "adtriba_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "adtriba-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.raw, local.big_query.datasets.transformed]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "pipedrive_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "pipedrive-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.raw, local.big_query.datasets.transformed]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "nrm_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "nrm-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.transformed]
    }
  ]
}

module "zendesk_auto_responder_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name          = "zendesk-auto-responder-sa"
  predefined_roles = [{ roles = ["roles/iam.serviceAccountUser"] }]
}

module "bm_product_starter_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name          = "backmarket-product-starter-sa"
  predefined_roles = [{ roles = ["roles/pubsub.publisher", "roles/pubsub.subscriber"] }]
}

module "bm_api_worker_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name            = "bm-api-worker-sa"
  predefined_roles   = [{ roles = ["roles/pubsub.publisher", "roles/pubsub.subscriber"] }]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "bm_compaction_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "bm-compaction-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.transformed]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "revenue_sync_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name            = "revenue-sync-sa"
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "google_ads_etl_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "google-ads-etl-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.transformed]
    },
    {
      roles          = ["roles/bigquery.dataViewer"],
      resource_names = [local.big_query.datasets.raw]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "microsoft_ads_etl_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "microsoft-ads-etl-sa"

  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.transformed]
    },
    {
      roles          = ["roles/bigquery.dataViewer"],
      resource_names = [local.big_query.datasets.raw]
    }
  ]
  custom_permissions = [{ permissions = ["logging.logEntries.create"] }]
}

module "analytics_db_refresh_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "analytics-db-refresh-sa"
  predefined_roles = [
    {
      roles = ["roles/cloudsql.client", "roles/iam.serviceAccountUser"]
    }
  ]
  custom_permissions = [
    { permissions = ["logging.logEntries.create"] },
    {
      permissions = local.is_production ? local.sql_instance_permissions : concat(local.sql_instance_permissions, local.sql_backup_permissions)
    }
  ]
}

module "datalake_ingestion_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "datalake-ingestion-sa"
  predefined_roles = [
    {
      roles = ["roles/cloudsql.client", "roles/iam.serviceAccountUser"]
    }
  ]
  custom_permissions = [
    { permissions = ["logging.logEntries.create"] },
    {
      permissions = local.is_production ? local.sql_instance_permissions : concat(local.sql_instance_permissions, local.sql_backup_permissions)
    }
  ]
}

# This is a special case to grant access from the production project
# to the staging service account analytics-db-refresh-sa
module "analytics_db_refresh_sa_production" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  should_create                 = local.is_production
  should_create_service_account = false

  sa_name = "<EMAIL>"
  custom_permissions = [
    {
      permissions = local.sql_backup_permissions
    }
  ]
}

# This is a special case to grant access from the production project
# to the staging service account datalake-ingestion-sa
module "datalake_ingestion_sa_production" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  should_create                 = local.is_production
  should_create_service_account = false

  sa_name = "<EMAIL>"
  custom_permissions = [
    {
      permissions = local.sql_backup_permissions
    }
  ]
}

module "query_terminator_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "query-terminator-sa"
}

module "proc_executor_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "proc-executor-sa"
}

module "dhl_shipping_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "dhl-etl-shipping-sa"
}

module "workflow_function_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "workflow-function-sa"

  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser", "roles/bigquery.resourceViewer"]
    },
    {
      roles          = ["roles/bigquery.dataViewer"],
      resource_names = [local.big_query.datasets.maintenance]
    }
  ]
}

module "category_slug_mapper_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "category-slug-mapper-sa"
  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.raw, local.big_query.datasets.transformed]
    }
  ]
}

module "platform_grafana" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "grafana"
  predefined_roles = [
    { roles = ["roles/bigquery.jobUser", "roles/monitoring.viewer"] },
    {
      roles          = ["roles/bigquery.dataEditor"],
      resource_names = [local.big_query.datasets.google_analytics, local.big_query.datasets.ga4]
    }
  ]
}

module "bm_product_crawler_workflow_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "bm-product-crawler-workflow-sa"

  predefined_roles   = [{ roles = local.workflow_roles }]
  custom_permissions = [{ permissions = local.workflow_permissions }]
}

module "analytics_workflow_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "analytics-workflow-sa"

  predefined_roles   = [{ roles = local.workflow_roles }]
  custom_permissions = [{ permissions = local.workflow_permissions }]
}

module "platform_etl_workflow_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "platform-etl-workflow-sa"

  predefined_roles   = [{ roles = local.workflow_roles }]
  custom_permissions = [{ permissions = local.workflow_permissions }]
}

module "unmanaged_workflow_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "unmanaged-workflow-sa"

  predefined_roles   = [{ roles = local.workflow_roles }]
  custom_permissions = [{ permissions = local.workflow_permissions }]
}

module "ranker_workflow_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "ranker-workflow-sa"

  predefined_roles   = [{ roles = local.workflow_roles }]
  custom_permissions = [{ permissions = local.workflow_permissions }]
}

module "ranker_sa" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"
  sa_name = "ranker-sa"

  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles = ["roles/bigquery.dataEditor"],
      resource_names = [
        local.big_query.datasets.transformed,
        local.big_query.datasets.reporting,
        local.big_query.datasets.ga4,
        local.big_query.datasets.google_analytics
      ]
    }
  ]
}

module "srp_circuit_breakers_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "srp-circuit-breakers-sa"
}


module "migrate_pg_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "migrate-pg-sa"
  predefined_roles = [
    {
      roles = ["roles/cloudsql.client", "roles/iam.serviceAccountUser"]
    }
  ]
  custom_permissions = [
    { permissions = ["logging.logEntries.create"] },

  ]
}

module "migrate_bq_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "migrate-bq-sa"
  predefined_roles = [
    {
      roles = ["roles/iam.serviceAccountUser"]
    },
    {
      roles = ["roles/bigquery.jobUser", "roles/bigquery.dataEditor", "roles/bigquery.resourceViewer"]
    }
  ]
  custom_permissions = [
    { permissions = ["logging.logEntries.create"] },
  ]
}

module "monetization_sa" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/service_account?ref=main"

  sa_name = "monetization-sa"

  predefined_roles = [
    {
      roles = ["roles/bigquery.jobUser"]
    },
    {
      roles = ["roles/bigquery.dataViewer"],
      resource_names = [
        local.big_query.datasets.transformed,
        local.big_query.datasets.reporting,
        local.big_query.datasets.funnelio
      ]
    }
  ]
}
