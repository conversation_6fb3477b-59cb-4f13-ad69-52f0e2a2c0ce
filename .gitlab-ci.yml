include: .gitlab/templates.yml

workflow:
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - when: always

# https://stackoverflow.com/a/67012275
.step_rules:
  rules:
    - &files_changed_rule
      changes:
        - $SRC_FILES_CHANGED
        - $TEST_FILES_CHANGED
      when: on_success
    - &main_branch_only_rule
      if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - $SRC_FILES_CHANGED
        - $TEST_FILES_CHANGED
      when: on_success
    - &main_branch_manual_rule
      if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - $SRC_FILES_CHANGED
        - $TEST_FILES_CHANGED
      when: manual

###########
# 📦 build
###########
build-app:
  stage: 📦 build
  extends:
    - .staging-vars
    - .gitlab-oidc
  variables:
    DOCKER_BUILDKIT: "1"
  script:
    - !reference [ .docker-login, script ]
    - docker pull ${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:latest
    - >
      docker build
      --force-rm
      --progress=plain
      --cache-from ${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:latest
      --tag "${APP_IMAGE}"
      .
    - docker push "${APP_IMAGE}"

build-db-schema:
  stage: 📦 build
  extends:
    - .staging-vars
    - .gitlab-oidc
  needs:
    - build-app
  variables:
    DOCKER_BUILDKIT: "1"
    IMAGE_NAME: ${ANALYTICS_DB_SCHEMA_IMAGE}
  rules:
    - *files_changed_rule
  script:
    - !reference [ .docker-login, script ]
    - docker pull ${CICD_DOCKER_REGISTRY}/${DB_SCHEMA_IMAGE_NAME}:latest
    - ./.gitlab/db_schema/build_db_schema_image.sh

########################
# 🧪 verify & prepare
########################
01-lint-code:
  stage: 🧪 verify & prepare
  image: ${APP_IMAGE}
  script:
    - pre-commit run --all-files

02-validate-terraform:
  stage: 🧪 verify & prepare
  extends: .terraform-validate
  variables:
    SRC_FILES_CHANGED: infra/**/*
    TEST_FILES_CHANGED: infra/**/*
  rules:
    - *files_changed_rule

03-unit-test:
  extends: .unit-test
  script:
    - pytest tests/unit --junitxml=test-report.xml -rA

04-sql-test:
  extends: .unit-test
  allow_failure: true
  services:
    - name: ${ANALYTICS_DB_SCHEMA_IMAGE}
      alias: ${DB_SCHEMA_IMAGE_NAME}
      command: [ "postgres", "-c", "max_connections=1000" ]
  variables:
    POSTGRES_HOST: ${DB_SCHEMA_IMAGE_NAME}
    POSTGRES_PORT: 5432
    SRC_FILES_CHANGED: ${SQL_FILES_CHANGED}
    TEST_FILES_CHANGED: ${SQL_TEST_FILES_CHANGED}
  rules:
    - *files_changed_rule
  script:
    - pytest tests/integration/test_local_sql --junitxml=test-report.xml -rA

05-plan-staging-infra:
  extends:
    - .staging-vars
    - .terraform-plan

06-plan-production-infra:
  extends:
    - .production-vars
    - .terraform-plan

###################
# 💣 deploy staging
###################
01-apply-staging-infra:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .terraform-apply
  when: manual
  needs:
    - 05-plan-staging-infra

02-migrate-staging-pg:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .migrate-pg
  rules:
    # Run migrations automatically only on a main branch
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - when: on_success
  needs:
    - 01-apply-staging-infra

03-migrate-staging-bq:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .migrate-bq
  rules:
    # Run migrations automatically only on a main branch
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - when: on_success
  needs:
    - 01-apply-staging-infra

######################
# 🧐 integration test
######################
00-analytics-db-refresh-test:
  extends: .integration-test
  script:
    - pytest tests/integration/test_analytics_db_refresh --junitxml=test-report.xml -rA
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      needs:
        - 01-apply-staging-infra
      changes:
        - src/analytics_db_refresh/**/*
        - src/common/**/*
        - src/sql-pg/**/*
      when: on_success
      allow_failure: false
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      needs:
        - 01-apply-staging-infra
      when: manual
      allow_failure: true

00-workflow-test:
  extends: .integration-test
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      needs:
        - 01-apply-staging-infra
        - 02-migrate-staging-pg
        - 03-migrate-staging-bq
        - 00-analytics-db-refresh-test
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      needs:
        - 01-apply-staging-infra
        - 02-migrate-staging-pg
        - 03-migrate-staging-bq
  script:
    - pytest tests/integration/test_workflow --junitxml=test-report.xml -rA

01-cloud-sql-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
    - 02-migrate-staging-pg
  script:
    - pytest tests/integration/test_cloud_sql --junitxml=test-report.xml -rA

02-bq-sql-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
    - 03-migrate-staging-bq
  script:
    - pytest tests/integration/test_big_query --junitxml=test-report.xml -rA

03-cloud-storage-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_storage --junitxml=test-report.xml -rA

04-cloud-sql-instance-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_sql_instance_client --junitxml=test-report.xml -rA

05-cloud-run-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_run* --junitxml=test-report.xml -rA

06-mattermost-client-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_mattermost_client --junitxml=test-report.xml -rA

07-logging-and-alerting-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_logging_client.py --junitxml=test-report.xml -rA
    - pytest tests/integration/test_logging_and_alerting --junitxml=test-report.xml -rA

08-pubsub-client-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_pubsub_client --junitxml=test-report.xml -rA

09-secret-and-parameter-manager-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_secret_and_parameter_manager --junitxml=test-report.xml -rA

#########################################################
# 🧐 Manual tests below
# Note that `when: manual` implies `allow_failure: true`
#########################################################
10-google-sheets-client-test:
  extends: .integration-test
  # Manual, connecting to Google Sheets API fails too often!
  when: manual
  script:
    - pytest tests/integration/test_google_sheets_client --junitxml=test-report.xml -rA

11-sftp-client-test:
  extends: .integration-test
  # Manual as we are connecting to live DHL sFTP server!
  when: manual
  script:
    - pytest tests/integration/test_sftp_client --junitxml=test-report.xml -rA

12-pipedrive-api-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Pipedrive API!
  when: manual
  script:
    - pytest tests/integration/test_pipedrive_api_client --junitxml=test-report.xml -rA

13-algolia-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Algolia API
  when: manual
  script:
    - pytest tests/integration/test_algolia_api --junitxml=test-report.xml -rA

14-platform-export-test:
  extends: .integration-test
  # Manual as implementation is in progress
  when: manual
  script:
    - pytest tests/integration/test_platform_export --junitxml=test-report.xml -rA

15-zendesk-client-test:
  extends: .integration-test
  # Manual as we are creating tickets in a Zendesk sandbox
  when: manual
  script:
    - pytest tests/integration/test_zendesk_client --junitxml=test-report.xml -rA

16-google-ads-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Google Ads API!
  when: manual
  script:
    - pytest tests/integration/test_performance_marketing/test_google_ads_client --junitxml=test-report.xml -rA

17-microsoft-ads-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Microsoft Ads API!
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_performance_marketing/test_microsoft_ads_client --junitxml=test-report.xml -rA

#######################
# 🚀 deploy production
#######################
01-apply-production-infra:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .terraform-apply
  rules:
    - *main_branch_manual_rule

02-migrate-production-pg:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .migrate-pg
  rules:
    - *main_branch_only_rule
  needs:
    - 01-apply-production-infra

03-migrate-production-bq:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .migrate-bq
  rules:
    - *main_branch_only_rule
  needs:
    - 01-apply-production-infra

#########
# ✨ tag
#########
.tag-image:
  stage: ✨ tag
  extends:
    - .staging-vars
    - .gitlab-oidc
  image: ${CICD_BUILD_IMAGE}
  rules:
    - *main_branch_only_rule
  script:
    - !reference [ .docker-login, script ]
    - docker pull ${IMAGE_NAME_FROM}
    - echo "Tagging image '${IMAGE_NAME_FROM}' to '${IMAGE_NAME_TO}'..."
    - docker tag ${IMAGE_NAME_FROM} ${IMAGE_NAME_TO}
    - docker push ${IMAGE_NAME_TO}

tag-db-schema-image:
  extends: .tag-image
  variables:
    IMAGE_NAME_FROM: ${ANALYTICS_DB_SCHEMA_IMAGE}
    IMAGE_NAME_TO: ${CICD_DOCKER_REGISTRY}/${DB_SCHEMA_IMAGE_NAME}:latest
    SRC_FILES_CHANGED: ${SQL_FILES_CHANGED}
    TEST_FILES_CHANGED: ${SQL_TEST_FILES_CHANGED}

tag-app-image:
  extends: .tag-image
  variables:
    IMAGE_NAME_FROM: ${APP_IMAGE}
    IMAGE_NAME_TO: ${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:latest

############
# 📨 notify
############
notify-failed:
  stage: 📨 notify
  when: on_failure
  script:
    - |
      export MM_MESSAGE=":exclamation: **GitLab pipeline has failed:** <$CI_PIPELINE_URL|$CI_PROJECT_NAME>\
      \n**User:** $GITLAB_USER_NAME\
      \n**Date:** $CI_JOB_STARTED_AT"
    - echo $MM_MESSAGE
    - |
      curl -i -X POST -H 'Content-type: application/json' --data "{
        \"text\": \"$MM_MESSAGE\"
      }" $MM_WEBHOOK;
  only:
    - main
