#### Start of variables

# Please make sure your name is as your Mattermost name,
# first letter should be lowercast, no '.' or "@" is allowed, only '_',
# So as example, @felix.schoenleitner would be here felix_schoenleitner!
# You can use 'test' if you are simply testing and don't want errors to appear on MM if you function fails
owner=bill_mourtakos
# The available criticality levels are p1 and p2, business critical and not, respectively
# All cloud functions will be initiated as p2 unless set otherwise
criticality=p1
# If the is a backup_owner for this function, also fill in the below tag, else leave empty as is
backup_owner=

function_name=performance-marketing-google-ads-steering
# https://cloud.google.com/sdk/gcloud/reference/functions/deploy
# 128MB, 256MB, 512MB, 1024MB, 2048MB, 4096MB, and 8192MB
timeout=300s
memory=1024MB
# The entry point of the function in the main.py file that you want to be executed by the cloud function!
# In the sample main.py file, the function run() is our entry point.
# You can change this at will!
entry_point=run
# You can use `gcloud projects list` to see the available projects
# The default for cloud function deployment would be refb-analytics
project_id=refb-analytics
# You can use `make list_accounts` to see the available service accounts
# The default for cloud function deployment would be
service_account=<EMAIL>
# The default time-cost effective region for cloud function deployment would be europe-west3
region=europe-west3
# The default (most common) language used in analytics team would be the stable python38
language=python312

# (Optional)The amount of logs to print, defaults to the last 30 lines
lines=30

# !! WARNING !! the schedule command (only) requires UNIX environment to run, like MacOS, Linux, Windows WSL etch
# This service account is only for authorization with the cloud scheduler, TODO fix -> pipeline@
SERVICE_ACCOUNT_EMAIL_AUTH=<EMAIL>
# The timezone for the cloud scheduler schedule
time_zone=Europe/Vienna
# The cron cloud scheduler schedule
cron=35,40 8,10-23 * * *
# Extra cron cloud schedule needed
cron_extra=10 0 * * *
retries=3
min_backoff=15s
max_doublings=3

# Including backup_owner only if it's not empty
LABELS = owner=$(owner),criticality=$(criticality)
ifneq ($(backup_owner),)
    LABELS := $(LABELS),backup_owner=$(backup_owner)
endif

###########################
###########################

# Check if the function exists
AUTH_FLAG := $(shell if ! gcloud functions describe $(function_name) --gen2 --region=$(region) >/dev/null 2>&1; then echo '--no-allow-unauthenticated'; fi)


.PHONY: _deploy
_deploy:
	gcloud functions deploy $(function_name) \
	 --gen2 \
	 --timeout=$(timeout) \
	 --memory=$(memory) \
	 --entry-point=$(entry_point) \
	 --trigger-http \
	 --service-account=$(service_account) \
	 --region=$(region) \
	 --runtime=$(language)\
	 --update-labels $(LABELS)\
	 $(AUTH_FLAG)

###########################
###########################

.PHONY: logs
logs:
	gcloud functions logs read $(function_name) --gen2 --region=$(region) --sort-by=TIME_UTC --limit=$(lines)

###########################
###########################

.PHONY: execute
execute:
	curl -m $(shell echo $(timeout) | sed 's/s$$//') -X POST $(shell gcloud functions describe $(function_name) --gen2 --region=$(region) --format 'value(serviceConfig.uri)') \
	-H "Authorization: bearer $(shell gcloud auth print-identity-token)" \
	-H "Content-Type: application/json" \
	-d '{"name": "Testing"}'

###########################
###########################

.PHONY: schedule_http
schedule_http:
    # For the basic cron schedule
	@echo "Attempting to delete job (ignore error if it doesn't exist)"
	- gcloud scheduler jobs delete "$(function_name)-schedule" \
	  --project="$(project_id)" \
	  --location="$(region)" \
	  --quiet

	@echo "Creating new job..."
	gcloud scheduler jobs create http $(function_name)-schedule \
	--schedule="$(cron)" \
	--time-zone="$(time_zone)" \
	--uri=$(shell gcloud functions describe $(function_name) --gen2 --region=europe-west3 --format 'value(serviceConfig.uri)') \
	--http-method=POST \
	--oidc-service-account-email=$(SERVICE_ACCOUNT_EMAIL_AUTH) \
	--location=$(region) \
	--max-retry-attempts=$(retries) \
	--min-backoff=$(min_backoff) \
	--max-doublings=$(max_doublings) \
	--project=$(project_id) \
	--attempt-deadline=30m

	# Extra cron schedule needed
	@echo "Attempting to delete job (ignore error if it doesn't exist)"
	- gcloud scheduler jobs delete "$(function_name)-schedule-extra" \
	  --project="$(project_id)" \
	  --location="$(region)" \
	  --quiet

	@echo "Creating new job..."
	gcloud scheduler jobs create http $(function_name)-schedule-extra \
	--schedule="$(cron_extra)" \
	--time-zone="$(time_zone)" \
	--uri=$(shell gcloud functions describe $(function_name) --gen2 --region=europe-west3 --format 'value(serviceConfig.uri)') \
	--http-method=POST \
	--oidc-service-account-email=$(SERVICE_ACCOUNT_EMAIL_AUTH) \
	--location=$(region) \
	--max-retry-attempts=$(retries) \
	--min-backoff=$(min_backoff) \
	--max-doublings=$(max_doublings) \
	--project=$(project_id) \
	--attempt-deadline=30m

###########################
###########################

.PHONY: delete_http_schedule
delete_http_schedule:
	gcloud scheduler jobs delete $(function_name)-schedule \
	--project=$(project_id) \
	--location=$(region) \
	--quiet

###########################
###########################

.PHONY: delete_function
delete_function:
	gcloud functions delete $(function_name) --region=$(region) --project=$(project_id)

###########################
###########################

.PHONY: deploy
deploy:
	$(MAKE) _deploy
	$(MAKE) schedule_http

###########################
###########################


.PHONY: delete
delete:
	$(MAKE) delete_function
	$(MAKE) delete_http_schedule

###########################
###########################
