from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.protobuf import field_mask_pb2
import gspread
from datetime import datetime
from collections import defaultdict
from refbetl import get_secret
import json


# ✅ Google Ads API Configuration
CONFIG = json.loads(get_secret("performance_marketing_google_ads"))
CONFIG["use_proto_plus"] = True

# ✅ Google Sheets API Konfiguration
google_sheet_secret = json.loads(get_secret("performance_marketing_google_sheet"))
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]
CLIENT_ID_SHEETS = google_sheet_secret["client_id"]
CLIENT_SECRET_SHEETS = google_sheet_secret["client_secret"]
REFRESH_TOKEN_SHEETS = google_sheet_secret["refresh_token_sheets"]

# ✅ Spreadsheet-Details
SHEET_ID = "17MH5TIxrD8wbiyvjQvPWsHHJy6-60e3qd5SKBUcfEVU"
TAB_NAME = "promotions upload"

# === SHEET VERBINDUNG ===
def get_sheet_data():
    creds = Credentials(
        None,
        refresh_token=REFRESH_TOKEN_SHEETS,
        token_uri="https://oauth2.googleapis.com/token",
        client_id=CLIENT_ID_SHEETS,
        client_secret=CLIENT_SECRET_SHEETS,
        scopes=SCOPES,
    )
    creds.refresh(Request())
    client = gspread.authorize(creds)
    sheet = client.open_by_key(SHEET_ID).worksheet(TAB_NAME)
    return sheet.get_all_records()

# === GRUPPIEREN NACH ITEM ID ===
def gruppiere_promotions(daten):
    grouped = defaultdict(list)
    for row in daten:
        grouped[str(row["Item ID"]).strip()].append(row)
    return grouped

# === VORHANDENE VERKNUEPFUNGEN FINDEN ===
def get_verknuepfte_kampagnen(client, customer_id, asset_resource):
    ga_service = client.get_service("GoogleAdsService")
    query = f"""
        SELECT campaign_asset.campaign, campaign_asset.asset, campaign_asset.status
        FROM campaign_asset
        WHERE campaign_asset.asset = '{asset_resource}'
    """
    response = ga_service.search(customer_id=customer_id, query=query)
    return {str(row.campaign_asset.campaign.split("/")[-1]): row.campaign_asset.status for row in response}

# === PROMOTION AKTUALISIEREN ===
def aktualisiere_promotion_asset(client, customer_id, asset_resource, daten):
    asset_service = client.get_service("AssetService")
    asset_operation = client.get_type("AssetOperation")
    asset = asset_operation.update
    asset.resource_name = asset_resource

    normalized = {k.strip().lower(): v for k, v in daten.items()}

    asset.final_urls.append(normalized.get("final url", ""))
    promo = asset.promotion_asset
    promo.promotion_target = normalized.get("promotion text", "")
    promo.language_code = normalized.get("promotion language", "")

    # === OCCASION ===
    occasion_raw = normalized.get("promotion occasion", "").strip()
    occasion_map = {
        "new year's": "NEW_YEARS",
        "chinese new year": "CHINESE_NEW_YEAR",
        "valentine's day": "VALENTINES_DAY",
        "easter": "EASTER",
        "mother's day": "MOTHERS_DAY",
        "father's day": "FATHERS_DAY",
        "labor day": "LABOR_DAY",
        "back to school": "BACK_TO_SCHOOL",
        "halloween": "HALLOWEEN",
        "black friday": "BLACK_FRIDAY",
        "cyber monday": "CYBER_MONDAY",
        "christmas": "CHRISTMAS",
        "boxing day": "BOXING_DAY",
        "independence day": "INDEPENDENCE_DAY",
        "national day": "NATIONAL_DAY",
        "end of season": "END_OF_SEASON",
        "winter sale": "WINTER_SALE",
        "summer sale": "SUMMER_SALE",
        "fall sale": "FALL_SALE",
        "spring sale": "SPRING_SALE",
        "ramadan": "RAMADAN",
        "eid al-fitr": "EID_AL_FITR",
        "eid al-adha": "EID_AL_ADHA",
        "singles day": "SINGLES_DAY",
        "women's day": "WOMENS_DAY",
        "holi": "HOLI",
        "parents' day": "PARENTS_DAY",
        "st. nicholas day": "ST_NICHOLAS_DAY",
        "carnival": "CARNIVAL",
        "epiphany": "EPIPHANY",
        "rosh hashanah": "ROSH_HASHANAH",
        "passover": "PASSOVER",
        "hanukkah": "HANUKKAH",
        "diwali": "DIWALI",
        "navratri": "NAVRATRI",
        "songkran": "SONGKRAN",
        "year end gift": "YEAR_END_GIFT"
    }
    if occasion_raw:
        mapped = occasion_map.get(occasion_raw.lower())
        if mapped:
            promo.occasion = client.enums.PromotionExtensionOccasionEnum[mapped]
        else:
            promo.occasion = client.enums.PromotionExtensionOccasionEnum(0)

    # === DISCOUNT MODIFIER ===
    modifier_raw = normalized.get("promotion discount modifier", "").strip()
    if modifier_raw.lower() == "up to":
        promo.discount_modifier = client.enums.PromotionExtensionDiscountModifierEnum.UP_TO

    # === START/END DATE ===
    try:
        start = datetime.strptime(normalized.get("start date", ""), "%d.%m.%Y").strftime("%Y-%m-%d")
        end = datetime.strptime(normalized.get("end date", ""), "%d.%m.%Y").strftime("%Y-%m-%d")
        promo.start_date = start
        promo.end_date = end
    except Exception as e:
        print(f"⚠️ Datumsfehler: {e}")

    # === FELDER DEFINIEREN ===
    field_mask = field_mask_pb2.FieldMask()
    for path in [
        "final_urls",
        "promotion_asset.promotion_target",
        "promotion_asset.language_code",
        "promotion_asset.occasion",
        "promotion_asset.start_date",
        "promotion_asset.end_date",
        "promotion_asset.discount_modifier",
    ]:
        field_mask.paths.append(path)

    # Optional: Promotion Code nur setzen, wenn nicht leer
    promo_code = normalized.get("promotion code", "").strip()
    if promo_code:
        promo.promotion_code = promo_code
        field_mask.paths.append("promotion_asset.promotion_code")

    asset_operation.update_mask.CopyFrom(field_mask)

    try:
        asset_service.mutate_assets(customer_id=customer_id, operations=[asset_operation])
        print("✅ Promotion aktualisiert")
    except GoogleAdsException as ex:
        print("❌ Fehler beim Aktualisieren der Promotion:")
        for error in ex.failure.errors:
            print(f"- {error.message}")
        print(f"Request ID: {ex.request_id}")

# === KAMPAGNENVERKNUEPFUNGEN AKTUALISIEREN ===
def aktualisiere_verknuepfungen(client, customer_id, asset_resource, eintraege):
    campaign_asset_service = client.get_service("CampaignAssetService")
    asset_field_type_enum = client.enums.AssetFieldTypeEnum
    campaign_asset_status_enum = client.enums.AssetLinkStatusEnum

    bestehende = get_verknuepfte_kampagnen(client, customer_id, asset_resource)
    ops = []
    asset_id = asset_resource.split("/")[-1]

    kampagnen_status_map = {
        str(entry["Campaign ID"]): str(entry.get("Status", "")).strip().lower()
        for entry in eintraege if entry.get("Campaign ID")
    }

    for kamp_id, status in bestehende.items():
        op = client.get_type("CampaignAssetOperation")
        campaign_asset = client.get_type("CampaignAsset")
        campaign_asset.resource_name = f"customers/{customer_id}/campaignAssets/{kamp_id}~{asset_id}~PROMOTION"
        neuer_status = kampagnen_status_map.get(kamp_id, "pause")
        campaign_asset.status = (
            campaign_asset_status_enum.ENABLED
            if neuer_status == "enabled"
            else campaign_asset_status_enum.PAUSED
        )
        op.update = campaign_asset
        op.update_mask.paths.append("status")
        ops.append(op)

    for kamp_id, status_string in kampagnen_status_map.items():
        if kamp_id not in bestehende:
            op = client.get_type("CampaignAssetOperation")
            asset = op.create
            asset.asset = asset_resource
            asset.field_type = asset_field_type_enum.PROMOTION
            asset.campaign = f"customers/{customer_id}/campaigns/{kamp_id}"
            asset.status = (
                campaign_asset_status_enum.ENABLED
                if status_string == "enabled"
                else campaign_asset_status_enum.PAUSED
            )
            ops.append(op)

    if ops:
        try:
            campaign_asset_service.mutate_campaign_assets(customer_id=customer_id, operations=ops)
        except GoogleAdsException as ex:
            print("❌ Fehler beim Synchronisieren der Kampagnenverknüpfung:")
            for error in ex.failure.errors:
                print(f"- {error.message}")
            print(f"Request ID: {ex.request_id}")

# === MAIN ===
def main():
    client = GoogleAdsClient.load_from_dict(CONFIG)
    daten = get_sheet_data()
    gruppiert = gruppiere_promotions(daten)

    for item_id, eintraege in gruppiert.items():
        account_id = str(eintraege[0]["Account ID"]).replace("-", "")
        asset_resource = f"customers/{account_id}/assets/{item_id}"
        print(f"\n🔄 Promo {item_id} → Konto {account_id}")

        try:
            aktualisiere_verknuepfungen(client, account_id, asset_resource, eintraege)
            aktualisiere_promotion_asset(client, account_id, asset_resource, eintraege[0])
        except GoogleAdsException as ex:
            print("❌ Fehler beim Aktualisieren:")
            for error in ex.failure.errors:
                print(f"- {error.message}")
            print(f"Request ID: {ex.request_id}")

if __name__ == "__main__":
    # This is an AD-HOC script, it will update PRODUCTION Google ADs if you run this!
    main()